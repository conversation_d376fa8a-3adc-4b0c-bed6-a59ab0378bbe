# 财经新闻爬虫系统

## 功能介绍

这是一个多源财经新闻爬虫系统，支持从以下三个主要财经媒体获取7×24小时快讯：

1. **同花顺** - 获取股市快讯和财经资讯
2. **东方财富** - 获取证券公告和市场动态  
3. **财联社** - 获取专业财经快讯

## 主要特性

- ✅ **多数据源支持** - 同时从三大财经媒体获取新闻
- ✅ **MySQL数据库存储** - 所有新闻数据持久化保存
- ✅ **Telegram推送** - 新新闻实时推送到Telegram频道
- ✅ **去重处理** - 避免重复推送相同新闻
- ✅ **配置化管理** - 支持灵活的配置管理
- ✅ **日志记录** - 完整的运行日志记录
- ✅ **错误处理** - 健壮的异常处理机制

## 文件结构

```
sprider/
├── ths.py              # 主程序文件
├── config.py           # 配置文件
├── requirements.txt    # 依赖包列表
├── README.md          # 使用说明
├── last_news.json     # 本地推送记录（自动生成）
└── news_crawler.log   # 运行日志（自动生成）
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

### 1. 数据库配置

在 `config.py` 中修改数据库连接信息：

```python
DB_CONFIG = {
    'host': 'your_host',
    'port': 3306,
    'user': 'your_user',
    'password': 'your_password',
    'database': 'your_database',
    'charset': 'utf8mb4'
}
```

### 2. Telegram配置

在 `config.py` 中配置Telegram Bot信息：

```python
TELEGRAM_CONFIG = {
    'bot_token': "your_bot_token",
    'chat_id': "your_chat_id"
}
```

### 3. 新闻源配置

可以在 `config.py` 中启用/禁用特定新闻源：

```python
NEWS_SOURCES = {
    'tonghuashun': {
        'enabled': True,  # 设为False禁用
        # ...
    },
    'eastmoney': {
        'enabled': True,
        # ...
    },
    'cailianshe': {
        'enabled': True,
        # ...
    }
}
```

## 数据库表结构

程序会自动创建 `financial_news` 表，包含以下字段：

- `id` - 自增主键
- `news_id` - 新闻唯一标识
- `source` - 新闻来源（同花顺/东方财富/财联社）
- `title` - 新闻标题
- `content` - 新闻内容
- `digest` - 新闻摘要
- `publish_time` - 发布时间
- `url` - 新闻链接
- `created_at` - 入库时间

## 使用方法

### 单次运行

```bash
python ths.py
```

### 定时运行

建议使用系统定时任务（如crontab）定期执行：

```bash
# 每5分钟执行一次
*/5 * * * * cd /path/to/sprider && python ths.py
```

## 运行流程

1. **连接数据库** - 建立MySQL连接并创建表结构
2. **获取新闻** - 并发从三个数据源获取最新新闻
3. **数据存储** - 将所有新闻保存到MySQL数据库
4. **去重过滤** - 基于本地记录过滤已推送的新闻
5. **Telegram推送** - 将新新闻推送到Telegram频道
6. **更新记录** - 更新本地推送记录

## 日志说明

程序运行时会生成详细的日志信息：

- 数据库连接状态
- 各数据源获取结果
- 数据保存情况
- Telegram推送结果
- 错误和异常信息

## 注意事项

1. **网络环境** - 确保服务器能正常访问各新闻源API
2. **数据库权限** - 确保数据库用户有创建表和读写权限
3. **Telegram配置** - 确保Bot Token和Chat ID配置正确
4. **频率控制** - 建议不要过于频繁地请求，避免被限流
5. **存储空间** - 定期清理旧的新闻数据，避免数据库过大

## 故障排除

### 快速诊断

```bash
# 快速测试所有数据源
python quick_test.py

# 详细API调试
python debug_apis.py

# 演示模式（无需数据库）
python run_demo.py
```

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置信息
   - 确认网络连通性
   - 验证用户权限

2. **新闻获取失败**
   - 检查网络连接
   - 查看API是否有变化
   - 检查请求头配置
   - 运行 `python debug_apis.py` 查看详细响应

3. **JSON解析错误**
   - 东方财富API返回格式: `var ajaxResult={...}`
   - 财联社API可能包含BOM字符
   - 使用调试脚本查看实际响应格式

4. **Telegram推送失败**
   - 验证Bot Token
   - 确认Chat ID正确
   - 检查网络连接

### API响应格式说明

- **同花顺**: 标准JSON格式
- **东方财富**: `var ajaxResult=` 或 `jQuery()` 包装的JSON
- **财联社**: 标准JSON，但可能包含BOM字符

### 查看日志

```bash
tail -f news_crawler.log
```

## 扩展开发

### 添加新的新闻源

1. 在 `config.py` 中添加新闻源配置
2. 在 `ths.py` 中实现对应的获取函数
3. 在 `get_all_news()` 函数中调用新函数

### 自定义推送格式

修改 `send_to_telegram()` 函数中的消息格式化逻辑。

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。
