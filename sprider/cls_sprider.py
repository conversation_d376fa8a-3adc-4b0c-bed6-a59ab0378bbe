import requests
import time
import os
import json
from datetime import datetime

# ===== 配置区域 =====
TELEGRAM_BOT_TOKEN = "YOUR_BOT_TOKEN"  # 替换为你的Telegram Bot Token
TELEGRAM_CHAT_ID = "YOUR_CHAT_ID"  # 替换为你的Telegram Chat ID
DATA_FILE = "cls_news.json"  # 存储已推送数据的文件
API_URL = "https://www.cls.cn/nodeapi/telegraphList"


def get_cls_news():
    """获取财联社 7*24 快讯数据 - 使用最新API"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        "Referer": "https://www.cls.cn/telegraph",
        "Accept": "application/json",
        "X-Requested-With": "XMLHttpRequest"
    }

    params = {
        "refresh_type": "1",  # 1表示获取最新数据
        "rn": "20",  # 获取数量
        "last_time": "0",  # 从最新开始
        "app": "CailianpressWeb",
        "sv": "9.0.0"
    }

    try:
        response = requests.get(API_URL, headers=headers, params=params, timeout=10)
        response.raise_for_status()

        # 解析响应数据
        data = response.json()
        if data.get("error") != 0:
            print(f"API返回错误: {data.get('message', '未知错误')}")
            return []

        # 提取新闻列表
        news_list = []
        for item in data.get("data", {}).get("roll_data", []):
            try:
                # 使用API返回的原始时间戳（10位秒级）
                ctime = item.get("ctime")
                if not ctime:
                    continue

                # 内容处理：优先使用content，没有则使用brief
                content = item.get("content", "")
                if not content:
                    content = item.get("brief", "")

                # 重要性判断
                importance = 0
                if "level" in item and item["level"] == "A":
                    importance = 3
                elif "is_top" in item and item["is_top"] == 1:
                    importance = 2

                news_list.append({
                    "id": str(item["id"]),
                    "title": item.get("title", ""),
                    "content": content,
                    "ctime": ctime,  # 直接使用API返回的时间戳
                    "importance": importance
                })
            except Exception as e:
                print(f"解析新闻条目失败: {e}")
                print(f"问题数据: {json.dumps(item, ensure_ascii=False)[:200]}")
                continue

        return news_list

    except Exception as e:
        print(f"获取财联社数据失败: {e}")
        return []


def filter_new_news(news_list):
    """过滤未推送过的新闻"""
    if not os.path.exists(DATA_FILE):
        return news_list

    try:
        with open(DATA_FILE, "r", encoding="utf-8") as f:
            sent_data = json.load(f)
            sent_ids = set(sent_data.get("sent_ids", []))
    except Exception as e:
        print(f"读取历史记录失败: {e}")
        sent_ids = set()

    # 按时间倒序排列，最新的在前面
    news_list.sort(key=lambda x: x["ctime"], reverse=True)

    # 过滤出未发送的新闻
    return [news for news in news_list if news["id"] not in sent_ids]


def format_news(news):
    """格式化快讯内容"""
    # 时间格式化
    ctime = datetime.fromtimestamp(news["ctime"]).strftime("%m-%d %H:%M")

    # 重要性标识
    importance = "🔥" * news.get("importance", 0)

    # 构建 Markdown 格式消息
    message = f"{importance} **{ctime}**\n"

    # 标题和内容处理
    title = news.get("title", "")
    content = news.get("content", "")

    # 如果标题和内容相同，只显示一次
    if title and content.startswith(title):
        message += f"{content}\n"
    elif title and content:
        message += f"**{title}**\n{content}\n"
    elif title:
        message += f"{title}\n"
    else:
        message += f"{content}\n"

    # 添加原文链接
    message += f"[原文链接](https://www.cls.cn/detail/{news['id']})"

    return message


def send_to_telegram(message):
    """发送消息到 Telegram"""
    api_url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    payload = {
        "chat_id": TELEGRAM_CHAT_ID,
        "text": message,
        "parse_mode": "Markdown",
        "disable_web_page_preview": True
    }

    try:
        response = requests.post(api_url, json=payload, timeout=10)
        if response.status_code == 200:
            return True
        else:
            print(f"Telegram 发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Telegram 发送失败: {e}")
        return False


def update_sent_records(news_list):
    """更新已发送记录"""
    if not news_list:
        return

    try:
        data = {"sent_ids": []}
        if os.path.exists(DATA_FILE):
            try:
                with open(DATA_FILE, "r", encoding="utf-8") as f:
                    data = json.load(f)
            except:
                pass

        # 添加新ID并限制记录数量
        existing_ids = set(data.get("sent_ids", []))
        new_ids = {item["id"] for item in news_list}
        all_ids = existing_ids | new_ids

        # 保留最近的1000条ID
        data["sent_ids"] = list(all_ids)[-1000:]

        with open(DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"更新记录失败: {e}")


def main():
    print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 开始获取财联社快讯...")

    # 获取新闻数据
    news_list = get_cls_news()
    if not news_list:
        print("未获取到快讯数据")
        return

    print(f"获取到 {len(news_list)} 条快讯")

    # 过滤新新闻
    new_news = filter_new_news(news_list)
    if not new_news:
        print("没有新快讯")
        return

    print(f"发现 {len(new_news)} 条新快讯")

    # 按时间顺序发送（旧→新）
    for news in sorted(new_news, key=lambda x: x["ctime"]):
        formatted = format_news(news)
        time_str = datetime.fromtimestamp(news["ctime"]).strftime("%H:%M")
        print(f"[{time_str}] 发送新闻: {news['title'][:30]}...")
        if send_to_telegram(formatted):
            time.sleep(0.5)  # 避免发送过快被限流
        else:
            print("发送失败，跳过此条新闻")

    # 更新发送记录
    update_sent_records(new_news)
    print(f"处理完成，更新 {len(new_news)} 条记录\n")


if __name__ == "__main__":
    main()