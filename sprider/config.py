# -*- coding: utf-8 -*-
"""
新闻爬虫配置文件
"""

# Telegram配置
TELEGRAM_CONFIG = {
    'bot_token': "**********************************************",
    'chat_id': "5898568800"
}

TELEGRAM_STOCK_NEWS_CONFIG = {
    'bot_token': "8279005776:AAGVLK4ZPbDAx6ICjAbVmgy-hQIHJCS6cIM",
    'chat_id': "5898568800"
}
# MySQL数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 3306,
    'user': 'remote_user',
    'password': 'wyl1975',
    'database': 'test_db',
    'charset': 'utf8mb4'
}

# 新闻源配置
NEWS_SOURCES = {
    'tonghuashun': {
        'name': '同花顺',
        'enabled': True,
        'url': 'https://news.10jqka.com.cn/tapp/news/push/stock',
        'params': {
            'cid': '73',
            'pagesize': '10',
            'track': 'news'
        }
    },
    'eastmoney': {
        'name': '东方财富',
        'enabled': True,  # 重新启用，使用修复的API
        'url': 'https://newsapi.eastmoney.com/kuaixun/v1/getlist_102_ajaxResult_10_1_.html',
        'params': {
            'cb': 'jQuery',
            'pagesize': '10',
            'client': 'web',
            'ut': '7w0eF3wzP5jz3'
        }
    },
    'eastmoney_stock': {
        'name': '东方财富股票',
        'enabled': True,
        'url': 'https://newsapi.eastmoney.com/kuaixun/v1/getlist_103_ajaxResult_10_1_.html',
        'params': {
            'cb': 'jQuery',
            'pagesize': '10',
            'client': 'web',
            'ut': '7w0eF3wzP5jz3'
        }
    },
    'cailianshe': {
        'name': '财联社',
        'enabled': True,  # 使用新的API端点
        'url': 'https://www.cls.cn/nodeapi/telegraphList',
        'params': {
            'refresh_type': '1',
            'rn': '10',
            'last_time': '0',
            'app': 'CailianpressWeb',
            'sv': '9.0.0'
        }
    }
}

# 通用请求头
COMMON_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# 其他配置
SETTINGS = {
    'data_file': 'last_news1.json',  # 本地记录文件
    'max_records': 10000000,  # 最大记录数量
    'request_timeout': 10,  # 请求超时时间
    'telegram_delay': 0.3,  # Telegram发送间隔
    'log_file': 'news_crawler.log'  # 日志文件
}