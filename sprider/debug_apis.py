# -*- coding: utf-8 -*-
"""
API调试脚本 - 用于测试各个新闻源的API响应
"""

import requests
import json
import time
from datetime import datetime

def test_tonghuashun_api():
    """测试同花顺API"""
    print("=" * 60)
    print("测试同花顺API")
    print("=" * 60)
    
    url = "https://news.10jqka.com.cn/tapp/news/push/stock"
    params = {
        'cid': '73',
        'pagesize': '5',
        'track': 'news'
    }
    headers = {ni
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://news.10jqka.com.cn/",
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON解析成功")
                print(f"数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                if isinstance(data, dict) and 'data' in data:
                    news_list = data['data'].get('list', [])
                    print(f"新闻数量: {len(news_list)}")
                    if news_list:
                        print(f"第一条新闻: {news_list[0]}")
            except Exception as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"请求失败: {e}")

def test_eastmoney_api():
    """测试东方财富API"""
    print("\n" + "=" * 60)
    print("测试东方财富API")
    print("=" * 60)
    
    url = "https://newsapi.eastmoney.com/kuaixun/v1/getlist_102_ajaxResult_50_1_.html"
    params = {
        'cb': 'jQuery',
        'pagesize': '5',
        'client': 'web',
        'ut': '7w0eF3wzP5jz3',
        '_': str(int(time.time() * 1000))
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://finance.eastmoney.com/",
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")
        
        if response.status_code == 200:
            text = response.text.strip()
            print(f"原始响应格式检测:")
            
            if text.startswith('var ajaxResult='):
                print("  - 检测到 var ajaxResult= 格式")
                json_text = text[15:].rstrip(';')
                print(f"  - 提取的JSON: {json_text[:100]}...")
                try:
                    data = json.loads(json_text)
                    print(f"  - JSON解析成功")
                    print(f"  - 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    if 'LivesList' in data:
                        print(f"  - LivesList数量: {len(data['LivesList'])}")
                        if data['LivesList']:
                            print(f"  - 第一条新闻: {data['LivesList'][0]}")
                except Exception as e:
                    print(f"  - JSON解析失败: {e}")
            
            elif text.startswith('jQuery'):
                print("  - 检测到 jQuery 回调格式")
                start = text.find('(')
                end = text.rfind(')')
                if start > 0 and end > start:
                    json_text = text[start+1:end]
                    print(f"  - 提取的JSON: {json_text[:100]}...")
                    try:
                        data = json.loads(json_text)
                        print(f"  - JSON解析成功")
                        print(f"  - 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    except Exception as e:
                        print(f"  - JSON解析失败: {e}")
            else:
                print("  - 未知格式，尝试直接解析JSON")
                try:
                    data = json.loads(text)
                    print(f"  - 直接JSON解析成功")
                    print(f"  - 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                except Exception as e:
                    print(f"  - 直接JSON解析失败: {e}")
        
    except Exception as e:
        print(f"请求失败: {e}")

def test_cailianshe_api():
    """测试财联社API"""
    print("\n" + "=" * 60)
    print("测试财联社API")
    print("=" * 60)
    
    url = "https://www.cls.cn/api/sw"
    params = {
        'app': 'CailianpressWeb',
        'os': 'web',
        'sv': '7.7.5',
        'rn': '5',
        'refresh_type': '1',
        'category': '24h'
    }
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://www.cls.cn/",
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")
        
        if response.status_code == 200:
            text = response.text.strip()
            print(f"响应格式检测:")
            
            # 检查BOM
            if text.startswith('\ufeff'):
                print("  - 检测到BOM，已移除")
                text = text[1:]
            
            # 查找JSON开始和结束
            json_start = text.find('{')
            json_end = text.rfind('}')
            print(f"  - JSON开始位置: {json_start}")
            print(f"  - JSON结束位置: {json_end}")
            
            if json_start >= 0 and json_end > json_start:
                json_text = text[json_start:json_end + 1]
                print(f"  - 提取的JSON: {json_text[:100]}...")
                try:
                    data = json.loads(json_text)
                    print(f"  - JSON解析成功")
                    print(f"  - 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    if 'data' in data:
                        print(f"  - data字段类型: {type(data['data'])}")
                        if isinstance(data['data'], dict) and 'roll_data' in data['data']:
                            roll_data = data['data']['roll_data']
                            print(f"  - roll_data数量: {len(roll_data)}")
                            if roll_data:
                                print(f"  - 第一条新闻: {roll_data[0]}")
                except Exception as e:
                    print(f"  - JSON解析失败: {e}")
            else:
                print("  - 未找到有效的JSON结构")
        
    except Exception as e:
        print(f"请求失败: {e}")

def main():
    """主测试函数"""
    print("🔍 新闻源API调试工具")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试各个API
    test_tonghuashun_api()
    test_eastmoney_api()
    test_cailianshe_api()
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")
    print("💡 根据上述输出调整代码中的数据解析逻辑")

if __name__ == "__main__":
    main()
