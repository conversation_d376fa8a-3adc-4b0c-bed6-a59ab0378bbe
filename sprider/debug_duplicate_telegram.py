# -*- coding: utf-8 -*-
"""
调试Telegram重复发送问题
"""

import sys
import os
from datetime import datetime
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_duplicate_news_detection():
    """测试重复新闻检测"""
    print("🧪 测试重复新闻检测")
    print("=" * 60)
    
    try:
        from ths import is_stock_news
        
        # 模拟用户提到的财联社新闻
        test_news = {
            'news_id': 'cls_test_001',
            'source': '财联社',
            'title': '本轮巴以冲突已致加沙地带60430人死亡',
            'content': '财联社8月2日电，据央视新闻，加沙地带卫生部门当地时间8月2日下午发布的统计数据显示，以军过去24小时在加沙地带的军事行动共导致98名巴勒斯坦人死亡，107',
            'digest': '加沙地带卫生部门统计数据显示，本轮巴以冲突已致60430人死亡',
            'publish_time': datetime.now(),
            'url': 'https://test.cls.cn',
            'raw_data': None  # 财联社没有stock字段
        }
        
        print(f"📰 测试新闻: {test_news['title']}")
        print(f"📝 新闻内容: {test_news['content'][:100]}...")
        
        # 检测是否被误判为股票新闻
        is_stock = is_stock_news(test_news)
        
        print(f"🔍 股票检测结果: {'是股票新闻' if is_stock else '不是股票新闻'}")
        
        if is_stock:
            print("❌ 财联社新闻被误判为股票新闻！")
            print("💡 这会导致发送到两个频道：主频道 + 股票频道")
            return False
        else:
            print("✅ 财联社新闻正确识别为非股票新闻")
            return True
            
    except Exception as e:
        print(f"❌ 重复新闻检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_keywords_in_news():
    """测试新闻中的股票关键词"""
    print("\n🧪 测试新闻中的股票关键词")
    print("=" * 60)
    
    # 财联社新闻文本
    news_text = "本轮巴以冲突已致加沙地带60430人死亡 财联社8月2日电，据央视新闻，加沙地带卫生部门当地时间8月2日下午发布的统计数据显示，以军过去24小时在加沙地带的军事行动共导致98名巴勒斯坦人死亡，107"
    
    # 股票关键词列表
    stock_keywords = [
        '股票', '股价', '股市', '股份', '上市', '退市', '停牌', '复牌',
        '涨停', '跌停', '涨幅', '跌幅', '成交量', '市值', '总市值',
        'a股', 'b股', 'h股', '港股', '美股', '科创板', '创业板', '主板',
        '上证', '深证', '沪深', '恒生', '纳斯达克', '道琼斯',
        '上证指数', '深证成指', '创业板指', '科创50',
        '上市公司', '龙头股', '白马股', '蓝筹股', '概念股', '题材股',
        '新股', 'ipo', '增发', '配股', '分红', '送股', '转股',
        'pe', 'pb', 'roe', 'eps', '净利润', '营收', '业绩', '财报',
        '年报', '季报', '中报', '业绩预告', '业绩快报',
        '基金', '私募', '公募', '券商', '机构', '北向资金', '外资',
        '证监会', '交易所', '监管', '重组', '并购', '收购', '举牌', '减持', '增持'
    ]
    
    found_keywords = []
    news_text_lower = news_text.lower()
    
    for keyword in stock_keywords:
        if keyword in news_text_lower:
            found_keywords.append(keyword)
    
    print(f"📝 新闻文本: {news_text}")
    print(f"🔍 找到的股票关键词: {found_keywords}")
    
    if found_keywords:
        print(f"⚠️ 发现 {len(found_keywords)} 个股票关键词，可能导致误判")
        for keyword in found_keywords:
            print(f"   - '{keyword}' 在文本中的位置: {news_text_lower.find(keyword)}")
        return False
    else:
        print("✅ 未发现股票关键词")
        return True

def test_telegram_config():
    """测试Telegram配置"""
    print("\n🧪 测试Telegram配置")
    print("=" * 60)
    
    try:
        from config import TELEGRAM_CONFIG, TELEGRAM_STOCK_NEWS_CONFIG
        
        print("📱 主频道配置:")
        print(f"   Bot Token: {TELEGRAM_CONFIG['bot_token'][:10]}...")
        print(f"   Chat ID: {TELEGRAM_CONFIG['chat_id']}")
        
        print("\n📈 股票频道配置:")
        print(f"   Bot Token: {TELEGRAM_STOCK_NEWS_CONFIG['bot_token'][:10]}...")
        print(f"   Chat ID: {TELEGRAM_STOCK_NEWS_CONFIG['chat_id']}")
        
        # 检查是否是同一个频道
        if TELEGRAM_CONFIG['chat_id'] == TELEGRAM_STOCK_NEWS_CONFIG['chat_id']:
            print("❌ 主频道和股票频道使用相同的Chat ID！")
            print("💡 这会导致同一条股票新闻发送到同一个频道两次")
            return False
        else:
            print("✅ 主频道和股票频道使用不同的Chat ID")
            return True
            
    except Exception as e:
        print(f"❌ Telegram配置测试失败: {e}")
        return False

def analyze_recent_news():
    """分析最近的新闻"""
    print("\n🧪 分析最近的新闻")
    print("=" * 60)
    
    try:
        from ths import get_all_news, is_stock_news
        
        print("🔄 获取最近新闻...")
        all_news = get_all_news()
        
        if not all_news:
            print("❌ 未获取到新闻")
            return False
        
        print(f"✅ 获取到 {len(all_news)} 条新闻")
        
        # 分析财联社新闻
        cls_news = [news for news in all_news if news['source'] == '财联社']
        
        print(f"\n📊 财联社新闻分析:")
        print(f"   总数: {len(cls_news)} 条")
        
        stock_cls_news = []
        for news in cls_news:
            if is_stock_news(news):
                stock_cls_news.append(news)
        
        print(f"   被识别为股票新闻: {len(stock_cls_news)} 条")
        
        if stock_cls_news:
            print(f"\n📈 被识别为股票的财联社新闻:")
            for i, news in enumerate(stock_cls_news[:3], 1):
                print(f"   {i}. {news['title'][:50]}...")
        
        # 查找可能的重复
        title_counts = {}
        for news in all_news:
            title = news['title']
            if title in title_counts:
                title_counts[title] += 1
            else:
                title_counts[title] = 1
        
        duplicates = {title: count for title, count in title_counts.items() if count > 1}
        
        if duplicates:
            print(f"\n⚠️ 发现重复标题:")
            for title, count in duplicates.items():
                print(f"   {title[:50]}... (出现{count}次)")
            return False
        else:
            print(f"\n✅ 未发现重复标题")
            return True
            
    except Exception as e:
        print(f"❌ 最近新闻分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Telegram重复发送问题调试")
    print(f"📅 调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 分析财联社新闻重复发送的原因")
    
    # 执行各项测试
    tests = [
        ("重复新闻检测", test_duplicate_news_detection),
        ("股票关键词检测", test_stock_keywords_in_news),
        ("Telegram配置检查", test_telegram_config),
        ("最近新闻分析", analyze_recent_news)
    ]
    
    results = []
    issues_found = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                issues_found.append(test_name)
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
            issues_found.append(test_name)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 重复发送问题调试结果")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if issues_found:
        print(f"\n⚠️ 发现的问题:")
        for issue in issues_found:
            print(f"   - {issue}")
        
        print(f"\n💡 可能的解决方案:")
        if "Telegram配置检查" in issues_found:
            print("   1. 检查主频道和股票频道是否使用了相同的Chat ID")
        if "重复新闻检测" in issues_found:
            print("   2. 优化股票新闻检测算法，避免误判")
        if "股票关键词检测" in issues_found:
            print("   3. 调整股票关键词列表，排除容易误判的词汇")
        if "最近新闻分析" in issues_found:
            print("   4. 检查去重机制是否正常工作")
    else:
        print(f"\n🎉 未发现明显问题，可能是临时网络问题或其他原因")

if __name__ == "__main__":
    main()
