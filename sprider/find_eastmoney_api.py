# -*- coding: utf-8 -*-
"""
寻找可用的东方财富API端点
"""

import requests
import json
import time
from datetime import datetime

def test_eastmoney_endpoints():
    """测试多个东方财富API端点"""
    print("🔍 寻找可用的东方财富API端点")
    print("=" * 60)
    
    # 可能的API端点
    endpoints = [
        {
            'name': '东方财富快讯API v1',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0',
                'pagesize': '20'
            }
        },
        {
            'name': '东方财富快讯API v2',
            'url': 'https://push2ex.eastmoney.com/getTopicZixunList',
            'params': {
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0',
                'pagesize': '20'
            }
        },
        {
            'name': '东方财富新闻API v3',
            'url': 'https://newsapi.eastmoney.com/api/getrollingnews',
            'params': {
                'pagesize': '20',
                'client': 'web'
            }
        },
        {
            'name': '东方财富快讯API v4',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'cb': 'jQuery',
                'pagesize': '20',
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0'
            }
        },
        {
            'name': '东方财富移动端API',
            'url': 'https://api.eastmoney.com/news/kuaixun',
            'params': {
                'pagesize': '20',
                'page': '1'
            }
        },
        {
            'name': '东方财富Web端API',
            'url': 'https://kuaixun.eastmoney.com/api/kuaixun',
            'params': {
                'pagesize': '20',
                'page': '1'
            }
        },
        {
            'name': '东方财富财经快讯',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'pagesize': '20',
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw'
            }
        },
        {
            'name': '东方财富滚动新闻',
            'url': 'https://newsapi.eastmoney.com/kuaixun/v1/getlist',
            'params': {
                'pagesize': '20',
                'client': 'web'
            }
        }
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        "Referer": "https://kuaixun.eastmoney.com/",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache"
    }
    
    working_endpoints = []
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"\n📡 测试端点 {i}: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        
        try:
            response = requests.get(
                endpoint['url'], 
                params=endpoint.get('params', {}), 
                headers=headers, 
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应长度: {len(response.text)}")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功")
                
                # 尝试解析JSON
                try:
                    # 处理可能的JSONP格式
                    text = response.text.strip()
                    
                    # 处理jQuery回调
                    if text.startswith('jQuery') and '(' in text:
                        start = text.find('(')
                        end = text.rfind(')')
                        if start > 0 and end > start:
                            text = text[start+1:end]
                    
                    data = json.loads(text)
                    print(f"   ✅ JSON解析成功")
                    
                    # 分析数据结构
                    news_count = analyze_news_structure(data)
                    if news_count > 0:
                        print(f"   🎉 找到 {news_count} 条新闻！")
                        working_endpoints.append((endpoint, data))
                        
                        # 显示样例数据
                        show_sample_news(data)
                    else:
                        print("   ⚠️ 未找到新闻数据")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   📄 响应内容: {response.text[:200]}...")
                    
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    return working_endpoints

def analyze_news_structure(data):
    """分析数据结构中的新闻数量"""
    news_count = 0
    
    if isinstance(data, dict):
        # 检查各种可能的数据结构
        possible_keys = ['data', 'result', 'list', 'news', 'items', 'LivesList', 'roll_data', 'content']
        
        for key in possible_keys:
            if key in data:
                value = data[key]
                if isinstance(value, list):
                    news_count = len(value)
                    print(f"   📰 找到{key}: {news_count}条")
                    break
                elif isinstance(value, dict):
                    # 递归检查嵌套结构
                    nested_count = analyze_news_structure(value)
                    if nested_count > 0:
                        news_count = nested_count
                        break
    elif isinstance(data, list):
        news_count = len(data)
        print(f"   📰 找到列表: {news_count}条")
    
    return news_count

def show_sample_news(data):
    """显示样例新闻"""
    try:
        news_list = []
        
        if isinstance(data, dict):
            for key in ['data', 'result', 'list', 'news', 'items', 'LivesList']:
                if key in data and isinstance(data[key], list):
                    news_list = data[key]
                    break
                elif key in data and isinstance(data[key], dict):
                    for subkey in ['list', 'data', 'items']:
                        if subkey in data[key] and isinstance(data[key][subkey], list):
                            news_list = data[key][subkey]
                            break
        elif isinstance(data, list):
            news_list = data
        
        if news_list and len(news_list) > 0:
            print(f"   📝 样例新闻:")
            sample = news_list[0]
            print(f"      字段: {list(sample.keys()) if isinstance(sample, dict) else type(sample)}")
            
            if isinstance(sample, dict):
                # 显示可能的标题字段
                title_fields = ['title', 'content', 'digest', 'summary', 'text']
                for field in title_fields:
                    if field in sample:
                        print(f"      {field}: {str(sample[field])[:50]}...")
                        break
                
                # 显示可能的时间字段
                time_fields = ['time', 'datetime', 'ctime', 'showtime', 'publish_time']
                for field in time_fields:
                    if field in sample:
                        print(f"      {field}: {sample[field]}")
                        break
                        
    except Exception as e:
        print(f"   ⚠️ 样例显示失败: {e}")

def generate_config(working_endpoints):
    """生成配置代码"""
    if not working_endpoints:
        print("\n❌ 未找到可用的API端点")
        return
    
    print("\n" + "=" * 60)
    print("🎉 找到可用的API端点！")
    print("=" * 60)
    
    for i, (endpoint, data) in enumerate(working_endpoints, 1):
        print(f"\n✅ 端点 {i}: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        print(f"   参数: {endpoint.get('params', {})}")
        
        # 生成配置代码
        print(f"\n💡 配置代码:")
        print(f"'eastmoney': {{")
        print(f"    'name': '东方财富',")
        print(f"    'enabled': True,")
        print(f"    'url': '{endpoint['url']}',")
        print(f"    'params': {endpoint.get('params', {})}")
        print(f"}},")

def main():
    """主函数"""
    print("🚀 东方财富API端点搜索工具")
    print(f"📅 搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    working_endpoints = test_eastmoney_endpoints()
    generate_config(working_endpoints)
    
    if not working_endpoints:
        print("\n💡 建议:")
        print("   1. 检查东方财富网站的网络请求")
        print("   2. 使用浏览器开发者工具分析API调用")
        print("   3. 考虑暂时禁用东方财富数据源")
        print("   4. 寻找其他财经新闻API替代")

if __name__ == "__main__":
    main()
