# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证修复后的新闻获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths import get_10jqka_news, get_eastmoney_news, get_cls_news
import logging

# 简化日志配置
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def quick_test():
    """快速测试所有新闻源"""
    print("🚀 快速测试新闻获取功能")
    print("=" * 50)
    
    results = {}
    
    # 测试同花顺
    print("\n1️⃣ 测试同花顺...")
    try:
        ths_news = get_10jqka_news()
        results['同花顺'] = len(ths_news)
        print(f"   ✅ 成功获取 {len(ths_news)} 条新闻")
        if ths_news:
            print(f"   📰 示例: {ths_news[0]['title'][:50]}...")
    except Exception as e:
        results['同花顺'] = f"失败: {e}"
        print(f"   ❌ 失败: {e}")
    
    # 测试东方财富
    print("\n2️⃣ 测试东方财富...")
    try:
        em_news = get_eastmoney_news()
        results['东方财富'] = len(em_news)
        print(f"   ✅ 成功获取 {len(em_news)} 条新闻")
        if em_news:
            print(f"   📰 示例: {em_news[0]['title'][:50]}...")
    except Exception as e:
        results['东方财富'] = f"失败: {e}"
        print(f"   ❌ 失败: {e}")
    
    # 测试财联社
    print("\n3️⃣ 测试财联社...")
    try:
        cls_news = get_cls_news()
        results['财联社'] = len(cls_news)
        print(f"   ✅ 成功获取 {len(cls_news)} 条新闻")
        if cls_news:
            print(f"   📰 示例: {cls_news[0]['title'][:50]}...")
    except Exception as e:
        results['财联社'] = f"失败: {e}"
        print(f"   ❌ 失败: {e}")
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    success_count = 0
    total_news = 0
    
    for source, result in results.items():
        if isinstance(result, int):
            print(f"✅ {source}: {result} 条新闻")
            success_count += 1
            total_news += result
        else:
            print(f"❌ {source}: {result}")
    
    print(f"\n🎯 成功率: {success_count}/3 ({success_count/3*100:.1f}%)")
    print(f"📰 总新闻数: {total_news} 条")
    
    if success_count == 3:
        print("\n🎉 所有数据源工作正常！")
        print("💡 可以运行完整程序: python ths.py")
    elif success_count > 0:
        print(f"\n⚠️  部分数据源可用 ({success_count}/3)")
        print("💡 建议检查失败的数据源")
    else:
        print("\n❌ 所有数据源都失败了")
        print("💡 请检查网络连接或运行 python debug_apis.py 进行详细调试")

if __name__ == "__main__":
    quick_test()
