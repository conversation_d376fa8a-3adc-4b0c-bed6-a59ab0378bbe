# -*- coding: utf-8 -*-
"""
新闻爬虫演示脚本 - 不需要数据库和Telegram配置
"""

import requests
import time
import json
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demo_get_tonghuashun_news():
    """演示获取同花顺新闻"""
    print("=" * 60)
    print("演示：获取同花顺7×24快讯")
    print("=" * 60)
    
    url = "https://news.10jqka.com.cn/tapp/news/push/stock"
    params = {
        "cid": "73",
        "pagesize": "5",  # 只获取5条用于演示
        "track": "news",
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://news.10jqka.com.cn/",
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json().get("data", {}).get("list", [])
        
        print(f"✅ 成功获取 {len(data)} 条同花顺新闻")
        
        for i, item in enumerate(data, 1):
            timestamp = int(item['ctime'])
            dt = datetime.fromtimestamp(timestamp)
            time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            
            print(f"\n📰 新闻 {i}:")
            print(f"   时间: {time_str}")
            print(f"   标题: {item['title']}")
            print(f"   摘要: {item.get('digest', '无摘要')[:100]}...")
        
        return data
        
    except Exception as e:
        print(f"❌ 获取同花顺新闻失败: {e}")
        return []

def demo_get_eastmoney_news():
    """演示获取东方财富新闻"""
    print("\n" + "=" * 60)
    print("演示：获取东方财富快讯")
    print("=" * 60)

    # 使用配置文件中的URL
    url = "https://newsapi.eastmoney.com/kuaixun/v1/getlist_102_ajaxResult_50_1_.html"
    params = {
        "cb": "jQuery",
        "pagesize": "5",
        "client": "web",
        "ut": "7w0eF3wzP5jz3",
        "_": str(int(time.time() * 1000))
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://finance.eastmoney.com/",
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()

        # 处理东方财富的特殊响应格式
        text = response.text.strip()
        print(f"📄 响应格式: {text[:50]}...")

        # 处理 var ajaxResult=... 格式
        if text.startswith('var ajaxResult='):
            text = text[15:].rstrip(';')
            print("🔧 检测到 var ajaxResult 格式，已处理")
        elif text.startswith('jQuery') and '(' in text:
            start = text.find('(')
            end = text.rfind(')')
            if start > 0 and end > start:
                text = text[start+1:end]
                print("🔧 检测到 jQuery 回调格式，已处理")

        try:
            data = json.loads(text)
            print("✅ JSON解析成功")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"📄 处理后的文本: {text[:200]}...")
            return []

        # 提取新闻列表
        news_list = []
        if 'LivesList' in data:
            news_list = data['LivesList']
        elif 'data' in data and isinstance(data['data'], list):
            news_list = data['data']

        print(f"✅ 成功获取 {len(news_list)} 条东方财富新闻")

        for i, item in enumerate(news_list[:3], 1):  # 只显示前3条
            print(f"\n📰 新闻 {i}:")
            print(f"   ID: {item.get('id', item.get('newsid', '无ID'))}")
            print(f"   标题: {item.get('title', '无标题')}")
            if 'showtime' in item:
                try:
                    dt = datetime.fromtimestamp(int(item['showtime']))
                    print(f"   时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                except:
                    print(f"   时间: {item.get('showtime', '无时间')}")

        return news_list

    except Exception as e:
        print(f"❌ 获取东方财富新闻失败: {e}")
        return []

def demo_get_cailianshe_news():
    """演示获取财联社新闻"""
    print("\n" + "=" * 60)
    print("演示：获取财联社快讯")
    print("=" * 60)

    url = "https://www.cls.cn/api/sw"
    params = {
        "app": "CailianpressWeb",
        "os": "web",
        "sv": "7.7.5",
        "rn": "5",  # 只获取5条
        "refresh_type": "1",
        "category": "24h"  # 使用配置文件中的参数
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://www.cls.cn/",
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()

        # 处理可能的格式问题
        text = response.text.strip()
        print(f"📄 响应格式: {text[:50]}...")

        # 移除可能的BOM
        if text.startswith('\ufeff'):
            text = text[1:]
            print("🔧 移除BOM字符")

        # 查找JSON边界
        json_start = text.find('{')
        json_end = text.rfind('}')

        if json_start >= 0 and json_end > json_start:
            text = text[json_start:json_end + 1]
            print("🔧 提取JSON内容")

        try:
            data = json.loads(text)
            print("✅ JSON解析成功")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"📄 处理后的文本: {text[:200]}...")
            return []

        # 提取新闻列表
        news_list = []
        if 'data' in data and 'roll_data' in data['data']:
            news_list = data['data']['roll_data']
        elif 'data' in data and isinstance(data['data'], list):
            news_list = data['data']

        print(f"✅ 成功获取 {len(news_list)} 条财联社新闻")

        for i, item in enumerate(news_list[:3], 1):  # 只显示前3条
            print(f"\n📰 新闻 {i}:")
            print(f"   ID: {item.get('id', '无ID')}")
            print(f"   标题: {item.get('title', '无标题')}")

            # 处理时间
            if 'ctime' in item:
                try:
                    dt = datetime.fromtimestamp(int(item['ctime']))
                    print(f"   时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                except:
                    print(f"   时间: {item.get('ctime', '无时间')}")

            print(f"   摘要: {item.get('brief', '无摘要')[:80]}...")

        return news_list

    except Exception as e:
        print(f"❌ 获取财联社新闻失败: {e}")
        return []

def main():
    """主演示函数"""
    print("🚀 财经新闻爬虫系统演示")
    print("📅 运行时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 统计信息
    total_news = 0
    sources = []
    
    # 演示各个数据源
    ths_news = demo_get_tonghuashun_news()
    if ths_news:
        total_news += len(ths_news)
        sources.append("同花顺")
    
    em_news = demo_get_eastmoney_news()
    if em_news:
        total_news += len(em_news)
        sources.append("东方财富")
    
    cls_news = demo_get_cailianshe_news()
    if cls_news:
        total_news += len(cls_news)
        sources.append("财联社")
    
    # 显示汇总信息
    print("\n" + "=" * 60)
    print("📊 演示结果汇总")
    print("=" * 60)
    print(f"✅ 成功连接的数据源: {', '.join(sources)}")
    print(f"📰 总共获取新闻: {total_news} 条")
    print(f"🔗 可用数据源数量: {len(sources)}/3")
    
    if len(sources) == 3:
        print("\n🎉 所有数据源连接正常！")
        print("💡 提示：")
        print("   1. 配置数据库信息后可运行完整版本")
        print("   2. 配置Telegram后可启用推送功能")
        print("   3. 运行 python ths.py 开始正式爬取")
    elif len(sources) > 0:
        print(f"\n⚠️  部分数据源可用 ({len(sources)}/3)")
        print("💡 建议检查网络连接或API变化")
    else:
        print("\n❌ 所有数据源连接失败")
        print("💡 请检查网络连接")

if __name__ == "__main__":
    main()
