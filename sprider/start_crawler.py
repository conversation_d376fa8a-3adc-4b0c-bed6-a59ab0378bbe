#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻爬虫启动脚本
提供友好的启动界面和选项
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 财经新闻爬虫系统")
    print("=" * 60)
    print("📅 当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📁 工作目录:", os.getcwd())
    print("🐍 Python版本:", sys.version.split()[0])
    print("=" * 60)

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")

    # 检查核心模块
    core_modules = {
        'requests': 'requests',
        'mysql-connector-python': 'mysql.connector',
        'schedule': 'schedule',
        'json': 'json',
        'datetime': 'datetime',
        'logging': 'logging'
    }

    missing_modules = []
    for package_name, module_name in core_modules.items():
        try:
            __import__(module_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            missing_modules.append(package_name)
            print(f"   ❌ {package_name}")

    if missing_modules:
        print(f"\n⚠️ 缺少依赖模块: {', '.join(missing_modules)}")
        print("💡 请运行: pip install -r requirements.txt")
        return False

    print("✅ 所有依赖项检查通过")
    return True

def check_config():
    """检查配置文件"""
    print("\n🔧 检查配置文件...")
    
    try:
        from config import DB_CONFIG, TELEGRAM_CONFIG, NEWS_SOURCES
        
        # 检查数据库配置
        if all(key in DB_CONFIG for key in ['host', 'user', 'password', 'database']):
            print("   ✅ 数据库配置完整")
        else:
            print("   ⚠️ 数据库配置不完整")
        
        # 检查Telegram配置
        if all(key in TELEGRAM_CONFIG for key in ['bot_token', 'chat_id']):
            print("   ✅ Telegram配置完整")
        else:
            print("   ⚠️ Telegram配置不完整")
        
        # 检查新闻源配置
        enabled_sources = [name for name, config in NEWS_SOURCES.items() if config.get('enabled', False)]
        print(f"   ✅ 启用的新闻源: {', '.join(enabled_sources)}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 配置文件导入失败: {e}")
        return False

def show_menu():
    """显示菜单选项"""
    print("\n📋 启动选项:")
    print("   1. 🔄 启动定时爬虫（每30秒执行一次）")
    print("   2. 🧪 运行演示模式（单次执行，无推送）")
    print("   3. 🔍 运行系统测试")
    print("   4. 📊 查看最近日志")
    print("   5. ⚙️ 配置检查")
    print("   0. 🚪 退出")

def run_scheduler():
    """运行定时爬虫"""
    print("\n🚀 启动定时爬虫系统...")
    print("💡 按 Ctrl+C 停止程序")
    print("📝 日志将保存到 news_crawler.log")
    print("-" * 40)
    
    try:
        # 运行主程序
        subprocess.run([sys.executable, "ths.py"])
    except KeyboardInterrupt:
        print("\n🛑 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")

def run_demo():
    """运行演示模式"""
    print("\n🧪 启动演示模式...")
    print("📝 仅获取新闻，不推送到Telegram")
    print("-" * 40)
    
    try:
        subprocess.run([sys.executable, "run_demo.py"])
    except Exception as e:
        print(f"\n❌ 演示模式运行异常: {e}")

def run_test():
    """运行系统测试"""
    print("\n🔍 运行系统测试...")
    print("-" * 40)
    
    try:
        subprocess.run([sys.executable, "final_test.py"])
    except Exception as e:
        print(f"\n❌ 测试运行异常: {e}")

def show_logs():
    """显示最近日志"""
    print("\n📊 最近日志内容:")
    print("-" * 40)
    
    log_file = "news_crawler.log"
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 显示最后20行
                for line in lines[-20:]:
                    print(line.rstrip())
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("📝 日志文件不存在，程序尚未运行过")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖和配置
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装依赖")
        return
    
    if not check_config():
        print("\n❌ 配置检查失败，请检查配置文件")
        return
    
    # 主循环
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == "1":
                run_scheduler()
            elif choice == "2":
                run_demo()
            elif choice == "3":
                run_test()
            elif choice == "4":
                show_logs()
            elif choice == "5":
                check_config()
            elif choice == "0":
                print("\n👋 再见！")
                break
            else:
                print("\n❌ 无效选择，请输入 0-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 操作异常: {e}")
        
        # 等待用户确认
        if choice in ["1", "2", "3"]:
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
