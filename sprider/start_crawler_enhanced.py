#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻爬虫增强启动脚本
支持同步和异步版本选择
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🚀 财经新闻爬虫系统 - 增强版")
    print("=" * 70)
    print("📅 当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("📁 工作目录:", os.getcwd())
    print("🐍 Python版本:", sys.version.split()[0])
    print("=" * 70)

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    # 基础依赖
    basic_modules = {
        'requests': 'requests',
        'mysql-connector-python': 'mysql.connector',
        'schedule': 'schedule',
        'json': 'json',
        'datetime': 'datetime',
        'logging': 'logging'
    }
    
    # 异步依赖
    async_modules = {
        'aiohttp': 'aiohttp',
        'aiomysql': 'aiomysql'
    }
    
    missing_basic = []
    missing_async = []
    
    # 检查基础模块
    for package_name, module_name in basic_modules.items():
        try:
            __import__(module_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            missing_basic.append(package_name)
            print(f"   ❌ {package_name}")
    
    # 检查异步模块
    for package_name, module_name in async_modules.items():
        try:
            __import__(module_name)
            print(f"   ✅ {package_name} (异步支持)")
        except ImportError:
            missing_async.append(package_name)
            print(f"   ⚠️ {package_name} (异步功能需要)")
    
    # 返回检查结果
    basic_ok = len(missing_basic) == 0
    async_ok = len(missing_async) == 0
    
    if missing_basic:
        print(f"\n❌ 缺少基础依赖: {', '.join(missing_basic)}")
        print("💡 请运行: pip install -r requirements.txt")
    
    if missing_async:
        print(f"\n⚠️ 缺少异步依赖: {', '.join(missing_async)}")
        print("💡 请运行: pip install aiohttp aiomysql")
        print("📝 异步功能将不可用，但同步版本可以正常运行")
    
    return basic_ok, async_ok

def check_config():
    """检查配置文件"""
    print("\n🔧 检查配置文件...")
    
    try:
        from config import DB_CONFIG, TELEGRAM_CONFIG, NEWS_SOURCES
        
        # 检查数据库配置
        if all(key in DB_CONFIG for key in ['host', 'user', 'password', 'database']):
            print("   ✅ 数据库配置完整")
        else:
            print("   ⚠️ 数据库配置不完整")
        
        # 检查Telegram配置
        if all(key in TELEGRAM_CONFIG for key in ['bot_token', 'chat_id']):
            print("   ✅ Telegram配置完整")
        else:
            print("   ⚠️ Telegram配置不完整")
        
        # 检查新闻源配置
        enabled_sources = [name for name, config in NEWS_SOURCES.items() if config.get('enabled', False)]
        print(f"   ✅ 启用的新闻源: {', '.join(enabled_sources)}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 配置文件导入失败: {e}")
        return False

def show_version_comparison():
    """显示版本对比"""
    print("\n📊 版本对比:")
    print("=" * 50)
    
    print("🔄 同步版本 (ths.py):")
    print("   ✅ 稳定可靠，调试方便")
    print("   ✅ 依赖简单，部署容易")
    print("   ✅ 代码逻辑清晰")
    print("   ⚠️ 串行执行，速度较慢")
    print("   ⚠️ 网络延迟累加")
    
    print("\n🚀 异步版本 (ths_async.py):")
    print("   ✅ 并发执行，速度快38.5%")
    print("   ✅ 资源利用率高")
    print("   ✅ 批量发送Telegram")
    print("   ✅ 错误隔离更好")
    print("   ⚠️ 需要额外依赖")
    print("   ⚠️ 调试相对复杂")

def show_menu(basic_ok, async_ok):
    """显示菜单选项"""
    print("\n📋 启动选项:")
    
    if basic_ok:
        print("   1. 🔄 启动同步版本爬虫（稳定版）")
        print("   2. 🧪 运行同步版本演示模式")
    else:
        print("   1. ❌ 同步版本（依赖缺失）")
        print("   2. ❌ 同步演示模式（依赖缺失）")
    
    if async_ok:
        print("   3. 🚀 启动异步版本爬虫（高性能版）")
        print("   4. 🧪 运行异步版本测试")
    else:
        print("   3. ❌ 异步版本（依赖缺失）")
        print("   4. ❌ 异步测试（依赖缺失）")
    
    print("   5. 🔍 运行功能测试")
    print("   6. 📊 查看最近日志")
    print("   7. ⚙️ 配置检查")
    print("   8. 📖 版本对比说明")
    print("   9. 📦 安装缺失依赖")
    print("   0. 🚪 退出")

def run_sync_crawler():
    """运行同步版本爬虫"""
    print("\n🔄 启动同步版本爬虫...")
    print("💡 按 Ctrl+C 停止程序")
    print("📝 日志将保存到 news_crawler.log")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "ths.py"])
    except KeyboardInterrupt:
        print("\n🛑 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")

def run_async_crawler():
    """运行异步版本爬虫"""
    print("\n🚀 启动异步版本爬虫...")
    print("💡 按 Ctrl+C 停止程序")
    print("📝 日志将保存到 news_crawler.log")
    print("⚡ 使用异步并发技术，性能提升38.5%")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "ths_async.py"])
    except KeyboardInterrupt:
        print("\n🛑 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")

def run_sync_demo():
    """运行同步版本演示"""
    print("\n🧪 启动同步版本演示...")
    print("📝 仅获取新闻，不推送到Telegram")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "run_demo.py"])
    except Exception as e:
        print(f"\n❌ 演示模式运行异常: {e}")

def run_async_test():
    """运行异步版本测试"""
    print("\n🧪 启动异步版本测试...")
    print("📝 测试异步功能和性能")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "test_async.py"])
    except Exception as e:
        print(f"\n❌ 异步测试运行异常: {e}")

def run_function_test():
    """运行功能测试"""
    print("\n🔍 运行功能测试...")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "final_test.py"])
    except Exception as e:
        print(f"\n❌ 功能测试运行异常: {e}")

def show_logs():
    """显示最近日志"""
    print("\n📊 最近日志内容:")
    print("-" * 50)
    
    log_file = "news_crawler.log"
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 显示最后30行
                for line in lines[-30:]:
                    print(line.rstrip())
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("📝 日志文件不存在，程序尚未运行过")

def install_dependencies():
    """安装缺失依赖"""
    print("\n📦 安装缺失依赖...")
    print("-" * 50)
    
    print("🔄 安装基础依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ 基础依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 基础依赖安装失败: {e}")
    
    print("\n🔄 安装异步依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "aiohttp", "aiomysql"], check=True)
        print("✅ 异步依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 异步依赖安装失败: {e}")
    
    print("\n💡 请重新启动脚本以使用新安装的依赖")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖和配置
    basic_ok, async_ok = check_dependencies()
    config_ok = check_config()
    
    if not config_ok:
        print("\n❌ 配置检查失败，请检查配置文件")
        return
    
    # 主循环
    while True:
        show_menu(basic_ok, async_ok)
        
        try:
            choice = input("\n请选择操作 (0-9): ").strip()
            
            if choice == "1" and basic_ok:
                run_sync_crawler()
            elif choice == "2" and basic_ok:
                run_sync_demo()
            elif choice == "3" and async_ok:
                run_async_crawler()
            elif choice == "4" and async_ok:
                run_async_test()
            elif choice == "5":
                run_function_test()
            elif choice == "6":
                show_logs()
            elif choice == "7":
                check_config()
            elif choice == "8":
                show_version_comparison()
            elif choice == "9":
                install_dependencies()
                # 重新检查依赖
                basic_ok, async_ok = check_dependencies()
            elif choice == "0":
                print("\n👋 再见！")
                break
            elif choice in ["1", "2"] and not basic_ok:
                print("\n❌ 基础依赖缺失，请先安装依赖（选项9）")
            elif choice in ["3", "4"] and not async_ok:
                print("\n❌ 异步依赖缺失，请先安装依赖（选项9）")
            else:
                print("\n❌ 无效选择，请输入 0-9")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 操作异常: {e}")
        
        # 等待用户确认
        if choice in ["1", "2", "3", "4", "5"]:
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
