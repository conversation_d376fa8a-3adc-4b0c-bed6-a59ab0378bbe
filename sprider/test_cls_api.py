# -*- coding: utf-8 -*-
"""
测试财联社API的不同端点
"""

import requests
import json
import time
from datetime import datetime

def test_cls_api_endpoints():
    """测试多个财联社API端点"""
    print("🔍 测试财联社API端点")
    print("=" * 60)
    
    # 不同的API端点配置
    endpoints = [
        {
            'name': '财联社快讯API v1',
            'url': 'https://www.cls.cn/api/sw',
            'method': 'GET',
            'params': {
                'app': 'CailianpressWeb',
                'os': 'web',
                'sv': '7.7.5',
                'rn': '10',
                'refresh_type': '1',
                'category': '24h'
            }
        },
        {
            'name': '财联社快讯API v2',
            'url': 'https://www.cls.cn/api/sw',
            'method': 'POST',
            'data': {
                'app': 'CailianpressWeb',
                'os': 'web',
                'sv': '7.7.5',
                'rn': '10',
                'refresh_type': '1',
                'category': '1'
            }
        },
        {
            'name': '财联社新闻列表API',
            'url': 'https://www.cls.cn/api/article/list',
            'method': 'GET',
            'params': {
                'app': 'CailianpressWeb',
                'os': 'web',
                'sv': '7.7.5',
                'rn': '10',
                'page': '1'
            }
        },
        {
            'name': '财联社滚动新闻API',
            'url': 'https://www.cls.cn/api/roll',
            'method': 'GET',
            'params': {
                'app': 'CailianpressWeb',
                'os': 'web',
                'sv': '7.7.5',
                'rn': '10'
            }
        },
        {
            'name': '财联社快讯API (简化)',
            'url': 'https://www.cls.cn/api/sw',
            'method': 'GET',
            'params': {
                'rn': '10'
            }
        }
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Referer": "https://www.cls.cn/",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache"
    }
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"\n📡 测试端点 {i}: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        print(f"   方法: {endpoint['method']}")
        
        try:
            if endpoint['method'] == 'POST':
                response = requests.post(
                    endpoint['url'], 
                    data=endpoint.get('data', {}), 
                    headers=headers, 
                    timeout=10
                )
            else:
                response = requests.get(
                    endpoint['url'], 
                    params=endpoint.get('params', {}), 
                    headers=headers, 
                    timeout=10
                )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应长度: {len(response.text)}")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功")
                
                # 尝试解析JSON
                try:
                    data = response.json()
                    print(f"   ✅ JSON解析成功")
                    print(f"   📊 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    
                    # 查找新闻数据
                    news_count = 0
                    if isinstance(data, dict):
                        if 'data' in data:
                            if isinstance(data['data'], dict) and 'roll_data' in data['data']:
                                news_count = len(data['data']['roll_data'])
                                print(f"   📰 找到roll_data: {news_count}条")
                                if news_count > 0:
                                    sample = data['data']['roll_data'][0]
                                    print(f"   📝 样本: {sample.get('title', 'N/A')[:50]}...")
                            elif isinstance(data['data'], list):
                                news_count = len(data['data'])
                                print(f"   📰 找到data列表: {news_count}条")
                        elif 'list' in data:
                            news_count = len(data['list'])
                            print(f"   📰 找到list: {news_count}条")
                        elif 'result' in data:
                            print(f"   📊 找到result字段")
                    
                    if news_count > 0:
                        print(f"   🎉 成功获取 {news_count} 条新闻！")
                        return endpoint, data
                    else:
                        print("   ⚠️ 未找到新闻数据")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   📄 响应内容: {response.text[:200]}...")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   📄 错误信息: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n❌ 所有端点测试完毕，未找到可用的API")
    return None, None

def main():
    """主函数"""
    print("🚀 财联社API端点测试工具")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    endpoint, data = test_cls_api_endpoints()
    
    if endpoint:
        print("\n" + "=" * 60)
        print("🎉 找到可用的API端点！")
        print("=" * 60)
        print(f"✅ 端点名称: {endpoint['name']}")
        print(f"✅ URL: {endpoint['url']}")
        print(f"✅ 方法: {endpoint['method']}")
        if endpoint['method'] == 'GET':
            print(f"✅ 参数: {endpoint.get('params', {})}")
        else:
            print(f"✅ 数据: {endpoint.get('data', {})}")
        print("\n💡 可以使用此配置更新代码中的财联社API设置")
    else:
        print("\n" + "=" * 60)
        print("😞 未找到可用的API端点")
        print("=" * 60)
        print("💡 建议:")
        print("   1. 检查财联社网站是否有新的API文档")
        print("   2. 尝试使用浏览器开发者工具分析网站请求")
        print("   3. 考虑使用其他财经新闻源")

if __name__ == "__main__":
    main()
