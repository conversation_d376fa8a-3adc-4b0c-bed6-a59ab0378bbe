# -*- coding: utf-8 -*-
"""
测试财联社移动端API
"""

import requests
import json
import time
from datetime import datetime

def test_cls_mobile_apis():
    """测试财联社移动端API"""
    print("📱 测试财联社移动端API")
    print("=" * 60)
    
    # 移动端API配置
    mobile_endpoints = [
        {
            'name': '财联社移动端快讯',
            'url': 'https://m.cls.cn/api/sw',
            'method': 'GET',
            'params': {
                'app': 'CailianpressWap',
                'os': 'web',
                'sv': '7.7.5',
                'rn': '10'
            }
        },
        {
            'name': '财联社移动端电报',
            'url': 'https://m.cls.cn/api/telegraph',
            'method': 'GET',
            'params': {
                'app': 'CailianpressWap',
                'rn': '10'
            }
        },
        {
            'name': '财联社移动端新闻',
            'url': 'https://m.cls.cn/api/news',
            'method': 'GET',
            'params': {
                'rn': '10',
                'page': '1'
            }
        },
        {
            'name': '财联社API (无参数)',
            'url': 'https://www.cls.cn/api/sw',
            'method': 'GET',
            'params': {}
        },
        {
            'name': '财联社NodeJS API',
            'url': 'https://www.cls.cn/nodeapi/telegraphs',
            'method': 'GET',
            'params': {
                'hasFirstVipArticle': '1',
                'lastTime': '',
                'rn': '10',
                'sign': ''
            }
        }
    ]
    
    # 模拟移动端浏览器
    mobile_headers = {
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
        "Referer": "https://m.cls.cn/",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "X-Requested-With": "XMLHttpRequest"
    }
    
    for i, endpoint in enumerate(mobile_endpoints, 1):
        print(f"\n📡 测试移动端点 {i}: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        
        try:
            response = requests.get(
                endpoint['url'], 
                params=endpoint.get('params', {}), 
                headers=mobile_headers, 
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应长度: {len(response.text)}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功")
                
                # 尝试解析JSON
                try:
                    data = response.json()
                    print(f"   ✅ JSON解析成功")
                    print(f"   📊 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    
                    # 查找新闻数据
                    news_count = 0
                    sample_news = None
                    
                    if isinstance(data, dict):
                        # 检查各种可能的数据结构
                        if 'data' in data:
                            if isinstance(data['data'], dict):
                                if 'roll_data' in data['data']:
                                    news_count = len(data['data']['roll_data'])
                                    sample_news = data['data']['roll_data'][0] if news_count > 0 else None
                                    print(f"   📰 找到roll_data: {news_count}条")
                                elif 'list' in data['data']:
                                    news_count = len(data['data']['list'])
                                    sample_news = data['data']['list'][0] if news_count > 0 else None
                                    print(f"   📰 找到data.list: {news_count}条")
                                elif 'telegraphs' in data['data']:
                                    news_count = len(data['data']['telegraphs'])
                                    sample_news = data['data']['telegraphs'][0] if news_count > 0 else None
                                    print(f"   📰 找到telegraphs: {news_count}条")
                            elif isinstance(data['data'], list):
                                news_count = len(data['data'])
                                sample_news = data['data'][0] if news_count > 0 else None
                                print(f"   📰 找到data列表: {news_count}条")
                        elif 'list' in data:
                            news_count = len(data['list'])
                            sample_news = data['list'][0] if news_count > 0 else None
                            print(f"   📰 找到list: {news_count}条")
                        elif 'telegraphs' in data:
                            news_count = len(data['telegraphs'])
                            sample_news = data['telegraphs'][0] if news_count > 0 else None
                            print(f"   📰 找到telegraphs: {news_count}条")
                    
                    if news_count > 0 and sample_news:
                        print(f"   🎉 成功获取 {news_count} 条新闻！")
                        print(f"   📝 样本标题: {sample_news.get('title', sample_news.get('brief', 'N/A'))[:50]}...")
                        print(f"   📝 样本字段: {list(sample_news.keys())}")
                        return endpoint, data
                    else:
                        print("   ⚠️ 未找到新闻数据")
                        print(f"   📄 完整响应: {json.dumps(data, ensure_ascii=False, indent=2)[:300]}...")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   📄 响应内容: {response.text[:200]}...")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   📄 错误信息: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(1)
    
    return None, None

def main():
    """主函数"""
    print("🚀 财联社移动端API测试工具")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    endpoint, data = test_cls_mobile_apis()
    
    if endpoint:
        print("\n" + "=" * 60)
        print("🎉 找到可用的移动端API！")
        print("=" * 60)
        print(f"✅ 端点名称: {endpoint['name']}")
        print(f"✅ URL: {endpoint['url']}")
        print(f"✅ 参数: {endpoint.get('params', {})}")
        print("\n💡 可以使用此配置更新代码中的财联社API设置")
    else:
        print("\n" + "=" * 60)
        print("😞 移动端API也不可用")
        print("=" * 60)
        print("💡 建议暂时禁用财联社数据源，使用其他两个数据源")

if __name__ == "__main__":
    main()
