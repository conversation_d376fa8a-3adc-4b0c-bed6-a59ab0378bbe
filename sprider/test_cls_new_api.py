# -*- coding: utf-8 -*-
"""
测试新的财联社API和去重逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
import logging
from ths import get_cls_news, get_all_news, deduplicate_news_list

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cls_new_api():
    """测试新的财联社API"""
    print("🧪 测试新的财联社API")
    print("=" * 60)
    
    try:
        cls_news = get_cls_news()
        
        if cls_news:
            print(f"✅ 成功获取财联社新闻: {len(cls_news)} 条")
            
            # 显示前3条新闻详情
            print("\n📰 前3条新闻详情:")
            for i, news in enumerate(cls_news[:3], 1):
                print(f"\n   {i}. 标题: {news['title']}")
                print(f"      来源: {news['source']}")
                print(f"      时间: {news['publish_time']}")
                print(f"      内容: {news['content'][:100]}...")
                print(f"      重要性: {news.get('importance', 0)}")
                print(f"      链接: {news['url']}")
                
            # 检查数据结构
            print(f"\n🔍 数据结构检查:")
            sample = cls_news[0]
            required_fields = ['news_id', 'source', 'title', 'content', 'publish_time', 'url']
            for field in required_fields:
                if field in sample:
                    print(f"   ✅ {field}: {type(sample[field])}")
                else:
                    print(f"   ❌ 缺少字段: {field}")
                    
            return True
        else:
            print("❌ 未获取到财联社新闻")
            return False
            
    except Exception as e:
        print(f"❌ 财联社API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_based_deduplication():
    """测试基于时间的去重逻辑"""
    print("\n" + "=" * 60)
    print("🧪 测试基于时间的去重逻辑")
    print("=" * 60)
    
    # 创建测试数据，包含重复新闻但时间不同
    base_time = datetime.now()
    test_news = [
        {
            'news_id': 'test_1',
            'source': '同花顺',
            'title': 'A股市场今日大涨',
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'publish_time': base_time - timedelta(minutes=10),  # 10分钟前
            'url': 'http://test1.com'
        },
        {
            'news_id': 'test_2',
            'source': '东方财富',
            'title': '【快讯】A股市场今日大涨！',  # 重复，但时间更晚
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'publish_time': base_time - timedelta(minutes=5),   # 5分钟前
            'url': 'http://test2.com'
        },
        {
            'news_id': 'test_3',
            'source': '财联社',
            'title': 'A股市场今日大涨',  # 重复，但时间最早
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'publish_time': base_time - timedelta(minutes=15),  # 15分钟前（最早）
            'url': 'http://test3.com'
        },
        {
            'news_id': 'test_4',
            'source': '同花顺',
            'title': '比亚迪销量创新高',  # 不重复
            'content': '比亚迪7月销量数据公布',
            'digest': '比亚迪销量',
            'publish_time': base_time - timedelta(minutes=8),
            'url': 'http://test4.com'
        }
    ]
    
    print(f"原始测试数据: {len(test_news)} 条")
    for i, news in enumerate(test_news, 1):
        print(f"   {i}. {news['source']} - {news['title']} - {news['publish_time'].strftime('%H:%M:%S')}")
    
    # 执行去重
    deduplicated = deduplicate_news_list(test_news)
    
    print(f"\n去重后数据: {len(deduplicated)} 条")
    for i, news in enumerate(deduplicated, 1):
        print(f"   {i}. {news['source']} - {news['title']} - {news['publish_time'].strftime('%H:%M:%S')}")
    
    # 验证结果
    print(f"\n🔍 去重结果验证:")
    
    # 应该保留最早的A股新闻（test_3）
    a_stock_news = [n for n in deduplicated if 'A股' in n['title']]
    if len(a_stock_news) == 1:
        earliest = a_stock_news[0]
        if earliest['news_id'] == 'test_3':
            print("   ✅ 正确保留了时间最早的A股新闻（财联社）")
        else:
            print(f"   ❌ 保留的不是最早的新闻，实际保留: {earliest['news_id']}")
    else:
        print(f"   ❌ A股新闻去重异常，保留了 {len(a_stock_news)} 条")
    
    # 应该保留比亚迪新闻（不重复）
    byd_news = [n for n in deduplicated if '比亚迪' in n['title']]
    if len(byd_news) == 1:
        print("   ✅ 正确保留了比亚迪新闻（不重复）")
    else:
        print(f"   ❌ 比亚迪新闻处理异常，保留了 {len(byd_news)} 条")
    
    return len(deduplicated) == 2  # 应该保留2条新闻

def test_three_sources_integration():
    """测试三个数据源的整合和去重"""
    print("\n" + "=" * 60)
    print("🧪 测试三个数据源整合和去重")
    print("=" * 60)
    
    try:
        all_news = get_all_news()
        
        if all_news:
            print(f"✅ 成功获取并去重新闻: {len(all_news)} 条")
            
            # 统计各数据源
            sources = {}
            for news in all_news:
                source = news['source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"\n📊 数据源分布:")
            for source, count in sources.items():
                print(f"   {source}: {count} 条")
            
            # 检查时间排序
            print(f"\n📅 时间排序检查:")
            is_sorted = True
            for i in range(len(all_news) - 1):
                if all_news[i]['publish_time'] < all_news[i + 1]['publish_time']:
                    is_sorted = False
                    break
            
            if is_sorted:
                print("   ✅ 新闻按时间正确排序（降序）")
            else:
                print("   ❌ 新闻排序有问题")
            
            # 显示前5条新闻
            print(f"\n📰 前5条新闻:")
            for i, news in enumerate(all_news[:5], 1):
                time_str = news['publish_time'].strftime('%H:%M:%S')
                print(f"   {i}. [{time_str}] {news['source']} - {news['title'][:40]}...")
            
            return True
        else:
            print("❌ 未获取到任何新闻")
            return False
            
    except Exception as e:
        print(f"❌ 三源整合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cls_importance_levels():
    """测试财联社重要性级别"""
    print("\n" + "=" * 60)
    print("🧪 测试财联社重要性级别")
    print("=" * 60)
    
    try:
        cls_news = get_cls_news()
        
        if cls_news:
            # 统计重要性级别
            importance_stats = {}
            for news in cls_news:
                importance = news.get('importance', 0)
                importance_stats[importance] = importance_stats.get(importance, 0) + 1
            
            print(f"📊 重要性级别分布:")
            for level in sorted(importance_stats.keys(), reverse=True):
                count = importance_stats[level]
                level_name = {3: "A级重要", 2: "置顶", 1: "一般", 0: "普通"}
                print(f"   {level_name.get(level, '未知')}: {count} 条")
            
            # 显示重要新闻
            important_news = [n for n in cls_news if n.get('importance', 0) > 0]
            if important_news:
                print(f"\n🔥 重要新闻 ({len(important_news)} 条):")
                for news in important_news:
                    importance = news.get('importance', 0)
                    emoji = "🔥" * importance
                    print(f"   {emoji} {news['title'][:50]}...")
            else:
                print("\n📝 当前没有标记为重要的新闻")
                
            return True
        else:
            print("❌ 未获取到财联社新闻")
            return False
            
    except Exception as e:
        print(f"❌ 重要性级别测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 财联社新API和去重逻辑测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    tests = [
        ("财联社新API", test_cls_new_api),
        ("基于时间的去重逻辑", test_time_based_deduplication),
        ("三源整合和去重", test_three_sources_integration),
        ("财联社重要性级别", test_cls_importance_levels)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("💡 主要改进:")
        print("   1. ✅ 使用新的财联社API端点")
        print("   2. ✅ 实现基于时间的去重逻辑")
        print("   3. ✅ 保留时间最早的重复新闻")
        print("   4. ✅ 支持财联社重要性级别")
        print("   5. ✅ 三个数据源完整整合")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")

if __name__ == "__main__":
    main()
