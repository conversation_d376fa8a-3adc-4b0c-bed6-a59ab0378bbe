# -*- coding: utf-8 -*-
"""
新闻爬虫测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ths import (
    get_10jqka_news, 
    get_eastmoney_news, 
    get_cls_news,
    create_connection,
    create_news_table,
    save_news_to_db
)
import logging

# 配置测试日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_news_sources():
    """测试各个新闻源"""
    print("=" * 50)
    print("测试新闻源获取功能")
    print("=" * 50)
    
    # 测试同花顺
    print("\n1. 测试同花顺新闻获取...")
    ths_news = get_10jqka_news()
    print(f"同花顺新闻数量: {len(ths_news)}")
    if ths_news:
        print(f"示例新闻: {ths_news[0]['title']}")
    
    # 测试东方财富
    print("\n2. 测试东方财富新闻获取...")
    em_news = get_eastmoney_news()
    print(f"东方财富新闻数量: {len(em_news)}")
    if em_news:
        print(f"示例新闻: {em_news[0]['title']}")
    
    # 测试财联社
    print("\n3. 测试财联社新闻获取...")
    cls_news = get_cls_news()
    print(f"财联社新闻数量: {len(cls_news)}")
    if cls_news:
        print(f"示例新闻: {cls_news[0]['title']}")
    
    return ths_news + em_news + cls_news

def test_database():
    """测试数据库连接和操作"""
    print("\n" + "=" * 50)
    print("测试数据库功能")
    print("=" * 50)
    
    # 测试连接
    print("\n1. 测试数据库连接...")
    conn = create_connection()
    if not conn:
        print("❌ 数据库连接失败")
        return False
    print("✅ 数据库连接成功")
    
    try:
        # 测试创建表
        print("\n2. 测试创建新闻表...")
        create_news_table(conn)
        print("✅ 新闻表创建/检查成功")
        
        # 测试数据插入
        print("\n3. 测试数据插入...")
        test_news = [{
            'news_id': 'test_001',
            'source': '测试源',
            'title': '测试新闻标题',
            'content': '这是一条测试新闻内容',
            'digest': '测试摘要',
            'publish_time': '2024-01-01 12:00:00',
            'url': 'https://test.com'
        }]
        
        saved_count = save_news_to_db(conn, test_news)
        print(f"✅ 测试数据保存成功: {saved_count} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    finally:
        if conn and conn.is_connected():
            conn.close()
            print("✅ 数据库连接已关闭")

def test_full_workflow():
    """测试完整工作流程"""
    print("\n" + "=" * 50)
    print("测试完整工作流程")
    print("=" * 50)
    
    # 获取新闻
    print("\n1. 获取所有新闻...")
    all_news = test_news_sources()
    
    if not all_news:
        print("❌ 未获取到任何新闻")
        return False
    
    print(f"✅ 总共获取到 {len(all_news)} 条新闻")
    
    # 测试数据库保存
    print("\n2. 测试保存到数据库...")
    conn = create_connection()
    if not conn:
        print("❌ 数据库连接失败")
        return False
    
    try:
        create_news_table(conn)
        saved_count = save_news_to_db(conn, all_news)
        print(f"✅ 成功保存 {saved_count} 条新闻到数据库")
        
        # 显示各来源统计
        source_stats = {}
        for news in all_news:
            source = news['source']
            source_stats[source] = source_stats.get(source, 0) + 1
        
        print("\n📊 各来源新闻统计:")
        for source, count in source_stats.items():
            print(f"  {source}: {count} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流程执行失败: {e}")
        return False
    finally:
        if conn and conn.is_connected():
            conn.close()

def main():
    """主测试函数"""
    print("🚀 开始新闻爬虫系统测试")
    
    # 测试各个组件
    db_ok = test_database()
    workflow_ok = test_full_workflow()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"数据库功能: {'✅ 正常' if db_ok else '❌ 异常'}")
    print(f"完整流程: {'✅ 正常' if workflow_ok else '❌ 异常'}")
    
    if db_ok and workflow_ok:
        print("\n🎉 所有测试通过！系统运行正常")
        print("\n💡 提示:")
        print("  - 可以运行 python ths.py 开始正式爬取")
        print("  - 建议设置定时任务定期执行")
        print("  - 查看 news_crawler.log 了解运行状态")
    else:
        print("\n⚠️  部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
