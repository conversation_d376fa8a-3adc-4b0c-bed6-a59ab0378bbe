# -*- coding: utf-8 -*-
"""
测试优化后的数据库操作功能
"""

import sys
import os
import asyncio
import time
from datetime import datetime, timedelta
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sync_database_optimization():
    """测试同步版本的数据库优化"""
    print("🧪 测试同步版本数据库优化")
    print("=" * 60)
    
    try:
        from ths import create_connection, create_news_table, save_news_to_db
        
        # 创建数据库连接
        conn = create_connection()
        if not conn:
            print("❌ 数据库连接失败")
            return False
        
        print("✅ 数据库连接成功")
        
        # 创建表
        create_news_table(conn)
        print("✅ 数据库表创建/检查成功")
        
        # 创建测试数据（包含重复项）
        base_time = datetime.now()
        test_news = [
            {
                'news_id': 'sync_opt_test_001',
                'source': '同步优化测试',
                'title': '同步优化测试新闻1',
                'content': '这是第一条同步优化测试新闻',
                'digest': '同步优化测试1',
                'publish_time': base_time,
                'url': 'https://sync-opt-test1.com'
            },
            {
                'news_id': 'sync_opt_test_002',
                'source': '同步优化测试',
                'title': '同步优化测试新闻2',
                'content': '这是第二条同步优化测试新闻',
                'digest': '同步优化测试2',
                'publish_time': base_time + timedelta(minutes=1),
                'url': 'https://sync-opt-test2.com'
            },
            {
                'news_id': 'sync_opt_test_001',  # 重复ID
                'source': '同步优化测试',
                'title': '重复的同步优化测试新闻1',
                'content': '这是重复的第一条同步优化测试新闻',
                'digest': '重复的同步优化测试1',
                'publish_time': base_time + timedelta(minutes=2),
                'url': 'https://sync-opt-test1-duplicate.com'
            }
        ]
        
        print(f"\n📊 测试数据: {len(test_news)} 条（包含1条重复）")
        
        # 第一次保存
        print("\n🔄 第一次保存测试数据...")
        saved_count1 = save_news_to_db(conn, test_news)
        print(f"✅ 第一次保存结果: {saved_count1} 条")
        
        # 第二次保存相同数据
        print("\n🔄 第二次保存相同数据...")
        saved_count2 = save_news_to_db(conn, test_news)
        print(f"✅ 第二次保存结果: {saved_count2} 条")
        
        # 验证结果
        if saved_count1 == 2 and saved_count2 == 0:
            print("✅ 同步版本数据库优化测试成功")
            print("   - 第一次正确保存了2条新数据")
            print("   - 第二次正确跳过了所有重复数据")
            result = True
        else:
            print(f"❌ 同步版本数据库优化测试失败")
            print(f"   - 期望: 第一次2条，第二次0条")
            print(f"   - 实际: 第一次{saved_count1}条，第二次{saved_count2}条")
            result = False
        
        # 关闭连接
        conn.close()
        return result
        
    except Exception as e:
        print(f"❌ 同步版本数据库优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_database_optimization():
    """测试异步版本的数据库优化"""
    print("\n🧪 测试异步版本数据库优化")
    print("=" * 60)
    
    try:
        from ths_async import create_async_connection, create_news_table_async, save_news_to_db_async
        
        # 创建异步数据库连接
        connection = await create_async_connection()
        if not connection:
            print("❌ 异步数据库连接失败")
            return False
        
        print("✅ 异步数据库连接成功")
        
        # 创建表
        await create_news_table_async(connection)
        print("✅ 异步数据库表创建/检查成功")
        
        # 创建测试数据（包含重复项）
        base_time = datetime.now()
        test_news = [
            {
                'news_id': 'async_opt_test_001',
                'source': '异步优化测试',
                'title': '异步优化测试新闻1',
                'content': '这是第一条异步优化测试新闻',
                'digest': '异步优化测试1',
                'publish_time': base_time,
                'url': 'https://async-opt-test1.com'
            },
            {
                'news_id': 'async_opt_test_002',
                'source': '异步优化测试',
                'title': '异步优化测试新闻2',
                'content': '这是第二条异步优化测试新闻',
                'digest': '异步优化测试2',
                'publish_time': base_time + timedelta(minutes=1),
                'url': 'https://async-opt-test2.com'
            },
            {
                'news_id': 'async_opt_test_001',  # 重复ID
                'source': '异步优化测试',
                'title': '重复的异步优化测试新闻1',
                'content': '这是重复的第一条异步优化测试新闻',
                'digest': '重复的异步优化测试1',
                'publish_time': base_time + timedelta(minutes=2),
                'url': 'https://async-opt-test1-duplicate.com'
            }
        ]
        
        print(f"\n📊 测试数据: {len(test_news)} 条（包含1条重复）")
        
        # 第一次保存
        print("\n🔄 第一次异步保存测试数据...")
        saved_count1 = await save_news_to_db_async(connection, test_news)
        print(f"✅ 第一次保存结果: {saved_count1} 条")
        
        # 第二次保存相同数据
        print("\n🔄 第二次异步保存相同数据...")
        saved_count2 = await save_news_to_db_async(connection, test_news)
        print(f"✅ 第二次保存结果: {saved_count2} 条")
        
        # 验证结果
        if saved_count1 == 2 and saved_count2 == 0:
            print("✅ 异步版本数据库优化测试成功")
            print("   - 第一次正确保存了2条新数据")
            print("   - 第二次正确跳过了所有重复数据")
            result = True
        else:
            print(f"❌ 异步版本数据库优化测试失败")
            print(f"   - 期望: 第一次2条，第二次0条")
            print(f"   - 实际: 第一次{saved_count1}条，第二次{saved_count2}条")
            result = False
        
        # 关闭连接
        connection.close()
        return result
        
    except Exception as e:
        print(f"❌ 异步版本数据库优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n🧪 测试数据库操作性能对比")
    print("=" * 60)
    
    try:
        from ths import create_connection, save_news_to_db
        
        # 创建大量测试数据
        base_time = datetime.now()
        large_test_data = []
        for i in range(50):  # 50条数据
            large_test_data.append({
                'news_id': f'perf_test_{i:03d}',
                'source': '性能测试',
                'title': f'性能测试新闻{i}',
                'content': f'这是第{i}条性能测试新闻内容',
                'digest': f'性能测试{i}',
                'publish_time': base_time + timedelta(minutes=i),
                'url': f'https://perf-test{i}.com'
            })
        
        print(f"📊 性能测试数据: {len(large_test_data)} 条")
        
        # 测试同步版本性能
        print("\n🔄 测试同步版本性能...")
        conn = create_connection()
        if conn:
            sync_start = time.time()
            sync_saved = save_news_to_db(conn, large_test_data)
            sync_end = time.time()
            sync_time = sync_end - sync_start
            
            print(f"⏱️ 同步版本: {sync_time:.3f} 秒，保存 {sync_saved} 条")
            conn.close()
        else:
            print("❌ 同步版本数据库连接失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        return False

async def test_real_data_optimization():
    """测试真实数据的优化效果"""
    print("\n🧪 测试真实数据优化效果")
    print("=" * 60)
    
    try:
        from ths_async import get_all_news_async, create_async_connection, save_news_to_db_async
        
        # 获取真实新闻数据
        print("🔄 获取真实新闻数据...")
        real_news = await get_all_news_async()
        
        if not real_news:
            print("❌ 未获取到真实新闻数据")
            return False
        
        print(f"✅ 获取到 {len(real_news)} 条真实新闻")
        
        # 连接数据库
        connection = await create_async_connection()
        if not connection:
            print("❌ 数据库连接失败")
            return False
        
        # 第一次保存
        print("\n🔄 第一次保存真实数据...")
        start_time = time.time()
        saved_count1 = await save_news_to_db_async(connection, real_news)
        end_time = time.time()
        first_save_time = end_time - start_time
        
        print(f"✅ 第一次保存: {saved_count1} 条，耗时 {first_save_time:.3f} 秒")
        
        # 第二次保存相同数据
        print("\n🔄 第二次保存相同数据...")
        start_time = time.time()
        saved_count2 = await save_news_to_db_async(connection, real_news)
        end_time = time.time()
        second_save_time = end_time - start_time
        
        print(f"✅ 第二次保存: {saved_count2} 条，耗时 {second_save_time:.3f} 秒")
        
        # 分析结果
        print(f"\n📊 优化效果分析:")
        print(f"   第一次保存: {saved_count1} 条新数据")
        print(f"   第二次保存: {saved_count2} 条新数据")
        print(f"   重复数据: {len(real_news) - saved_count1} 条")
        
        if saved_count2 == 0:
            print("✅ 重复数据检测正常工作")
        else:
            print("⚠️ 可能存在重复数据检测问题")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 真实数据优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 数据库优化功能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    tests = [
        ("同步版本数据库优化", test_sync_database_optimization),
        ("异步版本数据库优化", test_async_database_optimization),
        ("数据库操作性能对比", test_performance_comparison),
        ("真实数据优化效果", test_real_data_optimization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 数据库优化测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有数据库优化测试通过！")
        print("💡 优化要点:")
        print("   1. ✅ 预先查询已存在的news_id")
        print("   2. ✅ 只插入新的新闻数据")
        print("   3. ✅ 避免INSERT IGNORE的警告")
        print("   4. ✅ 提供详细的保存统计信息")
        print("   5. ✅ 同步和异步版本统一优化")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")

if __name__ == "__main__":
    asyncio.run(main())
