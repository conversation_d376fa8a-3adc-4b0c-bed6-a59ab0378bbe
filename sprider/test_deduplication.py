# -*- coding: utf-8 -*-
"""
测试新闻去重功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import logging
from ths import (
    clean_text, calculate_similarity, generate_content_hash,
    is_duplicate_news, deduplicate_news_list, get_all_news
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_text_cleaning():
    """测试文本清理功能"""
    print("🧪 测试文本清理功能")
    print("=" * 50)
    
    test_cases = [
        "【快讯】A股市场今日大涨！！！",
        "A股市场今日大涨",
        "  A股市场今日大涨  ",
        "A股市场今日大涨。。。",
        "【重要】A股市场今日大涨（附详情）",
    ]
    
    for i, text in enumerate(test_cases, 1):
        cleaned = clean_text(text)
        print(f"   {i}. 原文: {text}")
        print(f"      清理后: {cleaned}")
    
    print("✅ 文本清理测试完成\n")

def test_similarity_calculation():
    """测试相似度计算功能"""
    print("🧪 测试相似度计算功能")
    print("=" * 50)
    
    test_pairs = [
        ("A股市场今日大涨", "A股市场今日大涨"),  # 完全相同
        ("A股市场今日大涨", "【快讯】A股市场今日大涨！"),  # 基本相同
        ("A股市场今日大涨", "A股市场今日下跌"),  # 部分相同
        ("A股市场今日大涨", "美股市场昨日收盘"),  # 完全不同
        ("苹果公司发布新产品", "苹果公司发布最新产品"),  # 高度相似
        ("比亚迪7月销量创新高", "比亚迪七月销量创历史新高"),  # 语义相似
    ]
    
    for i, (text1, text2) in enumerate(test_pairs, 1):
        similarity = calculate_similarity(text1, text2)
        print(f"   {i}. 文本1: {text1}")
        print(f"      文本2: {text2}")
        print(f"      相似度: {similarity:.3f}")
        
        if similarity >= 0.85:
            print("      判断: 🔴 重复")
        elif similarity >= 0.7:
            print("      判断: 🟡 高度相似")
        elif similarity >= 0.5:
            print("      判断: 🟢 部分相似")
        else:
            print("      判断: ⚪ 不相似")
        print()
    
    print("✅ 相似度计算测试完成\n")

def test_content_hash():
    """测试内容哈希功能"""
    print("🧪 测试内容哈希功能")
    print("=" * 50)
    
    test_cases = [
        {"title": "A股市场今日大涨", "content": "详细内容", "digest": "摘要"},
        {"title": "【快讯】A股市场今日大涨！", "content": "详细内容", "digest": "摘要"},
        {"title": "A股市场今日大涨", "content": "不同的详细内容", "digest": "摘要"},
        {"title": "完全不同的标题", "content": "完全不同的内容", "digest": "完全不同的摘要"},
    ]
    
    hashes = []
    for i, case in enumerate(test_cases, 1):
        content_hash = generate_content_hash(
            case['title'], case['content'], case['digest']
        )
        hashes.append(content_hash)
        print(f"   {i}. 标题: {case['title']}")
        print(f"      哈希: {content_hash}")
    
    # 检查哈希重复
    print("\n🔍 哈希重复检查:")
    for i in range(len(hashes)):
        for j in range(i + 1, len(hashes)):
            if hashes[i] == hashes[j]:
                print(f"   发现重复哈希: 案例{i+1} 和 案例{j+1}")
    
    print("✅ 内容哈希测试完成\n")

def test_duplicate_detection():
    """测试重复检测功能"""
    print("🧪 测试重复检测功能")
    print("=" * 50)
    
    # 创建测试新闻列表
    existing_news = [
        {
            'title': 'A股市场今日大涨',
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'source': '同花顺',
            'publish_time': datetime.now()
        },
        {
            'title': '比亚迪7月销量创新高',
            'content': '比亚迪公布7月销量数据',
            'digest': '比亚迪销量',
            'source': '东方财富',
            'publish_time': datetime.now()
        }
    ]
    
    # 测试新闻
    test_news_items = [
        {
            'title': '【快讯】A股市场今日大涨！',  # 应该被识别为重复
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'source': '东方财富',
            'publish_time': datetime.now()
        },
        {
            'title': '比亚迪七月销量创历史新高',  # 应该被识别为重复
            'content': '比亚迪公布最新销量数据',
            'digest': '比亚迪销量数据',
            'source': '同花顺',
            'publish_time': datetime.now()
        },
        {
            'title': '特斯拉股价下跌',  # 不重复
            'content': '特斯拉股价今日下跌5%',
            'digest': '特斯拉下跌',
            'source': '财联社',
            'publish_time': datetime.now()
        }
    ]
    
    for i, news_item in enumerate(test_news_items, 1):
        is_dup = is_duplicate_news(news_item, existing_news)
        print(f"   {i}. 标题: {news_item['title']}")
        print(f"      判断: {'🔴 重复' if is_dup else '🟢 不重复'}")
    
    print("✅ 重复检测测试完成\n")

def test_news_list_deduplication():
    """测试新闻列表去重功能"""
    print("🧪 测试新闻列表去重功能")
    print("=" * 50)
    
    # 创建包含重复新闻的列表
    news_list = [
        {
            'title': 'A股市场今日大涨',
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'source': '同花顺',
            'publish_time': datetime.now()
        },
        {
            'title': '【快讯】A股市场今日大涨！',  # 重复
            'content': '今日A股三大指数全线上涨',
            'digest': 'A股大涨',
            'source': '东方财富',
            'publish_time': datetime.now()
        },
        {
            'title': '比亚迪7月销量创新高',
            'content': '比亚迪公布7月销量数据',
            'digest': '比亚迪销量',
            'source': '东方财富',
            'publish_time': datetime.now()
        },
        {
            'title': '比亚迪七月销量创历史新高',  # 重复
            'content': '比亚迪公布最新销量数据',
            'digest': '比亚迪销量数据',
            'source': '同花顺',
            'publish_time': datetime.now()
        },
        {
            'title': '特斯拉股价下跌',  # 不重复
            'content': '特斯拉股价今日下跌5%',
            'digest': '特斯拉下跌',
            'source': '财联社',
            'publish_time': datetime.now()
        }
    ]
    
    print(f"原始新闻数量: {len(news_list)}")
    
    # 执行去重
    deduplicated_list = deduplicate_news_list(news_list)
    
    print(f"去重后数量: {len(deduplicated_list)}")
    print("\n去重后的新闻:")
    for i, news in enumerate(deduplicated_list, 1):
        print(f"   {i}. {news['title']} (来源: {news['source']})")
    
    print("✅ 新闻列表去重测试完成\n")

def test_real_news_deduplication():
    """测试真实新闻去重功能"""
    print("🧪 测试真实新闻去重功能")
    print("=" * 50)
    
    try:
        # 获取真实新闻数据
        all_news = get_all_news()
        
        if all_news:
            print(f"✅ 成功获取并去重新闻: {len(all_news)} 条")
            
            # 显示前5条新闻
            print("\n前5条新闻:")
            for i, news in enumerate(all_news[:5], 1):
                print(f"   {i}. {news['source']} - {news['title'][:50]}...")
                if 'content_hash' in news:
                    print(f"      哈希: {news['content_hash'][:16]}...")
        else:
            print("❌ 未获取到新闻数据")
            
    except Exception as e:
        print(f"❌ 真实新闻测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 新闻去重功能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    test_text_cleaning()
    test_similarity_calculation()
    test_content_hash()
    test_duplicate_detection()
    test_news_list_deduplication()
    test_real_news_deduplication()
    
    print("=" * 50)
    print("🎯 测试总结")
    print("=" * 50)
    print("✅ 文本清理功能: 完成")
    print("✅ 相似度计算功能: 完成")
    print("✅ 内容哈希功能: 完成")
    print("✅ 重复检测功能: 完成")
    print("✅ 新闻列表去重功能: 完成")
    print("✅ 真实新闻去重功能: 完成")
    
    print("\n💡 去重机制说明:")
    print("   1. 基于标题相似度进行去重（阈值: 85%）")
    print("   2. 基于内容相似度进行去重（阈值: 85%）")
    print("   3. 基于摘要相似度进行去重（阈值: 85%）")
    print("   4. 生成内容哈希用于快速比较")
    print("   5. 保留时间最早的新闻，移除重复项")

if __name__ == "__main__":
    main()
