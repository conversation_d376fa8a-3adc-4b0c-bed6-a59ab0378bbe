# -*- coding: utf-8 -*-
"""
测试去重优先级功能
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_source_priority():
    """测试数据源优先级函数"""
    print("🧪 测试数据源优先级函数")
    print("=" * 60)
    
    try:
        from ths import get_source_priority
        
        sources = ['同花顺', '财联社', '东方财富', '未知来源']
        priorities = [get_source_priority(source) for source in sources]
        
        print("📊 数据源优先级:")
        for source, priority in zip(sources, priorities):
            print(f"   {source}: {priority}")
        
        # 验证优先级顺序
        expected_order = [1, 2, 3, 999]
        if priorities == expected_order:
            print("✅ 数据源优先级设置正确")
            return True
        else:
            print(f"❌ 数据源优先级错误，期望: {expected_order}，实际: {priorities}")
            return False
            
    except Exception as e:
        print(f"❌ 数据源优先级测试失败: {e}")
        return False

def test_deduplication_priority():
    """测试去重优先级逻辑"""
    print("\n🧪 测试去重优先级逻辑")
    print("=" * 60)
    
    try:
        from ths import deduplicate_news_list
        
        # 创建测试新闻数据（相同内容，不同来源）
        base_time = datetime.now()
        
        test_news = [
            {
                'news_id': 'test_001_ths',
                'source': '同花顺',
                'title': '测试新闻：某公司股价上涨',
                'content': '某公司股价今日上涨5%，市场反应积极',
                'digest': '某公司股价上涨5%',
                'publish_time': base_time + timedelta(minutes=2),  # 较晚
                'url': 'https://ths-test.com'
            },
            {
                'news_id': 'test_001_cls',
                'source': '财联社',
                'title': '测试新闻：某公司股价上涨',
                'content': '某公司股价今日上涨5%，市场反应积极',
                'digest': '某公司股价上涨5%',
                'publish_time': base_time,  # 最早
                'url': 'https://cls-test.com'
            },
            {
                'news_id': 'test_001_em',
                'source': '东方财富',
                'title': '测试新闻：某公司股价上涨',
                'content': '某公司股价今日上涨5%，市场反应积极',
                'digest': '某公司股价上涨5%',
                'publish_time': base_time + timedelta(minutes=1),  # 中间
                'url': 'https://em-test.com'
            }
        ]
        
        print(f"📰 测试数据: {len(test_news)} 条相同内容的新闻")
        for news in test_news:
            print(f"   {news['source']}: {news['publish_time'].strftime('%H:%M:%S')}")
        
        # 执行去重
        deduplicated = deduplicate_news_list(test_news)
        
        print(f"\n🔄 去重结果: {len(deduplicated)} 条")
        
        if len(deduplicated) == 1:
            kept_news = deduplicated[0]
            print(f"✅ 保留的新闻: {kept_news['source']} - {kept_news['publish_time'].strftime('%H:%M:%S')}")
            
            # 验证是否保留了同花顺的新闻（优先级最高）
            if kept_news['source'] == '同花顺':
                print("✅ 正确保留了优先级最高的同花顺新闻")
                return True
            else:
                print(f"❌ 应该保留同花顺新闻，但保留了: {kept_news['source']}")
                return False
        else:
            print(f"❌ 去重失败，应该只保留1条，实际保留了: {len(deduplicated)} 条")
            return False
            
    except Exception as e:
        print(f"❌ 去重优先级测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_same_source_time_priority():
    """测试相同数据源的时间优先级"""
    print("\n🧪 测试相同数据源的时间优先级")
    print("=" * 60)
    
    try:
        from ths import deduplicate_news_list
        
        base_time = datetime.now()
        
        # 创建相同来源、相同内容、不同时间的新闻
        test_news = [
            {
                'news_id': 'test_002_cls_1',
                'source': '财联社',
                'title': '测试新闻：市场动态',
                'content': '市场今日表现良好，投资者情绪乐观',
                'digest': '市场表现良好',
                'publish_time': base_time + timedelta(minutes=5),  # 较晚
                'url': 'https://cls-test-1.com'
            },
            {
                'news_id': 'test_002_cls_2',
                'source': '财联社',
                'title': '测试新闻：市场动态',
                'content': '市场今日表现良好，投资者情绪乐观',
                'digest': '市场表现良好',
                'publish_time': base_time,  # 最早
                'url': 'https://cls-test-2.com'
            }
        ]
        
        print(f"📰 测试数据: {len(test_news)} 条相同来源的新闻")
        for news in test_news:
            print(f"   {news['source']}: {news['publish_time'].strftime('%H:%M:%S')}")
        
        # 执行去重
        deduplicated = deduplicate_news_list(test_news)
        
        print(f"\n🔄 去重结果: {len(deduplicated)} 条")
        
        if len(deduplicated) == 1:
            kept_news = deduplicated[0]
            print(f"✅ 保留的新闻: {kept_news['source']} - {kept_news['publish_time'].strftime('%H:%M:%S')}")
            
            # 验证是否保留了时间最早的新闻
            if kept_news['publish_time'] == base_time:
                print("✅ 正确保留了时间最早的新闻")
                return True
            else:
                print(f"❌ 应该保留最早的新闻，但保留了较晚的")
                return False
        else:
            print(f"❌ 去重失败，应该只保留1条，实际保留了: {len(deduplicated)} 条")
            return False
            
    except Exception as e:
        print(f"❌ 相同数据源时间优先级测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complex_deduplication():
    """测试复杂去重场景"""
    print("\n🧪 测试复杂去重场景")
    print("=" * 60)
    
    try:
        from ths import deduplicate_news_list
        
        base_time = datetime.now()
        
        # 创建复杂的测试数据
        test_news = [
            # 第一组重复新闻（优先级测试）
            {
                'news_id': 'test_003_em',
                'source': '东方财富',
                'title': '新闻A：重要经济数据发布',
                'content': '重要经济数据今日发布，市场关注',
                'digest': '重要经济数据发布',
                'publish_time': base_time,
                'url': 'https://em-test-a.com'
            },
            {
                'news_id': 'test_003_cls',
                'source': '财联社',
                'title': '新闻A：重要经济数据发布',
                'content': '重要经济数据今日发布，市场关注',
                'digest': '重要经济数据发布',
                'publish_time': base_time + timedelta(minutes=1),
                'url': 'https://cls-test-a.com'
            },
            # 第二组重复新闻（时间测试）
            {
                'news_id': 'test_004_ths_1',
                'source': '同花顺',
                'title': '新闻B：公司业绩公告',
                'content': '某公司发布业绩公告，超出预期',
                'digest': '公司业绩超预期',
                'publish_time': base_time + timedelta(minutes=3),
                'url': 'https://ths-test-b1.com'
            },
            {
                'news_id': 'test_004_ths_2',
                'source': '同花顺',
                'title': '新闻B：公司业绩公告',
                'content': '某公司发布业绩公告，超出预期',
                'digest': '公司业绩超预期',
                'publish_time': base_time + timedelta(minutes=2),  # 更早
                'url': 'https://ths-test-b2.com'
            },
            # 独立新闻（不重复）
            {
                'news_id': 'test_005_unique',
                'source': '财联社',
                'title': '新闻C：独立新闻',
                'content': '这是一条独立的新闻，不与其他新闻重复',
                'digest': '独立新闻',
                'publish_time': base_time + timedelta(minutes=4),
                'url': 'https://cls-test-c.com'
            }
        ]
        
        print(f"📰 测试数据: {len(test_news)} 条新闻（包含重复和独立）")
        
        # 执行去重
        deduplicated = deduplicate_news_list(test_news)
        
        print(f"\n🔄 去重结果: {len(deduplicated)} 条")
        
        # 分析结果
        sources_kept = [news['source'] for news in deduplicated]
        titles_kept = [news['title'] for news in deduplicated]
        
        print(f"📊 保留的新闻:")
        for news in deduplicated:
            print(f"   {news['source']}: {news['title']} - {news['publish_time'].strftime('%H:%M:%S')}")
        
        # 验证结果
        expected_count = 3  # 应该保留3条：新闻A(财联社)、新闻B(同花顺早期)、新闻C(独立)
        
        if len(deduplicated) == expected_count:
            print(f"✅ 去重数量正确: {expected_count} 条")
            
            # 检查具体保留的新闻
            checks = []
            
            # 检查新闻A是否保留了财联社版本（优先级高于东方财富）
            news_a = [n for n in deduplicated if '新闻A' in n['title']]
            if news_a and news_a[0]['source'] == '财联社':
                checks.append(True)
                print("✅ 新闻A正确保留了财联社版本（优先级高于东方财富）")
            else:
                checks.append(False)
                print("❌ 新闻A应该保留财联社版本")
            
            # 检查新闻B是否保留了时间更早的同花顺版本
            news_b = [n for n in deduplicated if '新闻B' in n['title']]
            if news_b and news_b[0]['publish_time'] == base_time + timedelta(minutes=2):
                checks.append(True)
                print("✅ 新闻B正确保留了时间更早的版本")
            else:
                checks.append(False)
                print("❌ 新闻B应该保留时间更早的版本")
            
            # 检查独立新闻是否保留
            news_c = [n for n in deduplicated if '新闻C' in n['title']]
            if news_c:
                checks.append(True)
                print("✅ 独立新闻C正确保留")
            else:
                checks.append(False)
                print("❌ 独立新闻C应该保留")
            
            return all(checks)
        else:
            print(f"❌ 去重数量错误，期望: {expected_count}，实际: {len(deduplicated)}")
            return False
            
    except Exception as e:
        print(f"❌ 复杂去重场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_deduplication():
    """测试异步版本的去重功能"""
    print("\n🧪 测试异步版本的去重功能")
    print("=" * 60)
    
    try:
        from ths_async import deduplicate_news_list, get_source_priority
        
        # 测试异步版本的优先级函数
        priority_ths = get_source_priority('同花顺')
        priority_cls = get_source_priority('财联社')
        priority_em = get_source_priority('东方财富')
        
        print(f"📊 异步版本数据源优先级:")
        print(f"   同花顺: {priority_ths}")
        print(f"   财联社: {priority_cls}")
        print(f"   东方财富: {priority_em}")
        
        if priority_ths < priority_cls < priority_em:
            print("✅ 异步版本优先级设置正确")
            return True
        else:
            print("❌ 异步版本优先级设置错误")
            return False
            
    except Exception as e:
        print(f"❌ 异步版本去重测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 去重优先级功能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 验证数据源优先级：同花顺 > 财联社 > 东方财富")
    
    # 执行各项测试
    tests = [
        ("数据源优先级函数", test_source_priority),
        ("去重优先级逻辑", test_deduplication_priority),
        ("相同数据源时间优先级", test_same_source_time_priority),
        ("复杂去重场景", test_complex_deduplication),
        ("异步版本去重功能", test_async_deduplication)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 去重优先级测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 去重优先级功能测试全部通过！")
        print("💡 优先级规则:")
        print("   1. ✅ 数据源优先级：同花顺(1) > 财联社(2) > 东方财富(3)")
        print("   2. ✅ 相同数据源时，保留时间最早的新闻")
        print("   3. ✅ 同步和异步版本行为一致")
        print("   4. ✅ 复杂场景处理正确")
        print("   5. ✅ 独立新闻正常保留")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")

if __name__ == "__main__":
    asyncio.run(main())
