# -*- coding: utf-8 -*-
"""
测试东方财富API端点
"""

import requests
import json
import time
from datetime import datetime

def test_eastmoney_api_endpoints():
    """测试多个东方财富API端点"""
    print("🔍 测试东方财富API端点")
    print("=" * 60)
    
    # 不同的API端点配置
    endpoints = [
        {
            'name': '东方财富快讯API v1',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0',
                'pagesize': '20',
                'fields': 'f1,f2,f3,f4'
            }
        },
        {
            'name': '东方财富快讯API v2',
            'url': 'https://newsapi.eastmoney.com/api/getrollingnews',
            'params': {
                'pagesize': '20',
                'client': 'web'
            }
        },
        {
            'name': '东方财富快讯API v3',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'cb': '',
                'pagesize': '20',
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw'
            }
        },
        {
            'name': '东方财富新闻列表API',
            'url': 'https://newsapi.eastmoney.com/kuaixun/v1/getlist',
            'params': {
                'pagesize': '20',
                'client': 'web',
                'type': '102'
            }
        },
        {
            'name': '东方财富滚动新闻API',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'pagesize': '20',
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0'
            }
        },
        {
            'name': '东方财富移动端API',
            'url': 'https://api.eastmoney.com/news/kuaixun',
            'params': {
                'pagesize': '20',
                'page': '1'
            }
        }
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        "Referer": "https://finance.eastmoney.com/",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache"
    }
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"\n📡 测试端点 {i}: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        
        try:
            response = requests.get(
                endpoint['url'], 
                params=endpoint.get('params', {}), 
                headers=headers, 
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应长度: {len(response.text)}")
            print(f"   Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功")
                
                # 检查响应内容类型
                content_type = response.headers.get('Content-Type', '')
                if 'application/json' in content_type:
                    try:
                        data = response.json()
                        print(f"   ✅ JSON解析成功")
                        print(f"   📊 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                        
                        # 查找新闻数据
                        news_count = analyze_news_data(data)
                        if news_count > 0:
                            print(f"   🎉 成功获取 {news_count} 条新闻！")
                            return endpoint, data
                        else:
                            print("   ⚠️ 未找到新闻数据")
                            
                    except json.JSONDecodeError as e:
                        print(f"   ❌ JSON解析失败: {e}")
                        print(f"   📄 响应内容: {response.text[:200]}...")
                        
                elif 'text/html' in content_type:
                    print("   ⚠️ 返回HTML内容，可能需要处理JSONP")
                    # 尝试处理JSONP格式
                    text = response.text.strip()
                    if try_parse_jsonp(text):
                        print("   ✅ JSONP解析成功")
                        return endpoint, None
                    else:
                        print("   ❌ JSONP解析失败")
                        
                else:
                    print(f"   ⚠️ 未知内容类型: {content_type}")
                    print(f"   📄 响应内容: {response.text[:200]}...")
                    
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   📄 错误信息: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print("\n❌ 所有端点测试完毕，未找到可用的API")
    return None, None

def analyze_news_data(data):
    """分析数据中的新闻数量"""
    news_count = 0
    
    if isinstance(data, dict):
        # 检查各种可能的数据结构
        possible_keys = ['data', 'result', 'list', 'news', 'items', 'LivesList', 'roll_data']
        
        for key in possible_keys:
            if key in data:
                value = data[key]
                if isinstance(value, list):
                    news_count = len(value)
                    print(f"   📰 找到{key}: {news_count}条")
                    if news_count > 0:
                        sample = value[0]
                        print(f"   📝 样本字段: {list(sample.keys()) if isinstance(sample, dict) else type(sample)}")
                    break
                elif isinstance(value, dict):
                    # 递归检查嵌套结构
                    nested_count = analyze_news_data(value)
                    if nested_count > 0:
                        news_count = nested_count
                        break
    elif isinstance(data, list):
        news_count = len(data)
        print(f"   📰 找到列表: {news_count}条")
        if news_count > 0:
            sample = data[0]
            print(f"   📝 样本字段: {list(sample.keys()) if isinstance(sample, dict) else type(sample)}")
    
    return news_count

def try_parse_jsonp(text):
    """尝试解析JSONP格式"""
    try:
        # 处理各种JSONP格式
        if text.startswith('var ') and '=' in text:
            # var ajaxResult = {...}
            start = text.find('=') + 1
            end = text.rfind(';') if text.endswith(';') else len(text)
            json_text = text[start:end].strip()
        elif '(' in text and text.endswith(')'):
            # callback({...})
            start = text.find('(') + 1
            end = text.rfind(')')
            json_text = text[start:end].strip()
        else:
            json_text = text
        
        data = json.loads(json_text)
        news_count = analyze_news_data(data)
        return news_count > 0
        
    except Exception as e:
        print(f"   ❌ JSONP解析异常: {e}")
        return False

def test_specific_endpoint():
    """测试特定的已知端点"""
    print("\n" + "=" * 60)
    print("🧪 测试特定已知端点")
    print("=" * 60)
    
    # 基于网络搜索的已知端点
    known_endpoints = [
        {
            'name': '东方财富官方快讯API',
            'url': 'https://push2.eastmoney.com/api/qt/cmsapi_roll/get',
            'params': {
                'cb': 'jQuery',
                'pagesize': '20',
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0'
            }
        },
        {
            'name': '东方财富新版API',
            'url': 'https://push2ex.eastmoney.com/getTopicZixunList',
            'params': {
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'dpt': 'wz.yw',
                'pageindex': '0',
                'pagesize': '20'
            }
        }
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Referer": "https://finance.eastmoney.com/",
        "Accept": "*/*"
    }
    
    for endpoint in known_endpoints:
        print(f"\n🔍 测试: {endpoint['name']}")
        try:
            response = requests.get(endpoint['url'], params=endpoint['params'], headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            print(f"   内容长度: {len(response.text)}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功")
                print(f"   📄 响应预览: {response.text[:100]}...")
                
                # 尝试解析
                if try_parse_jsonp(response.text):
                    print("   🎉 找到可用的API端点！")
                    return endpoint
                    
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")
    
    return None

def main():
    """主函数"""
    print("🚀 东方财富API端点测试工具")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试多个端点
    endpoint, data = test_eastmoney_api_endpoints()
    
    if endpoint:
        print("\n" + "=" * 60)
        print("🎉 找到可用的API端点！")
        print("=" * 60)
        print(f"✅ 端点名称: {endpoint['name']}")
        print(f"✅ URL: {endpoint['url']}")
        print(f"✅ 参数: {endpoint.get('params', {})}")
        print("\n💡 可以使用此配置更新代码中的东方财富API设置")
    else:
        # 测试特定端点
        specific_endpoint = test_specific_endpoint()
        
        if specific_endpoint:
            print("\n" + "=" * 60)
            print("🎉 找到特定可用端点！")
            print("=" * 60)
            print(f"✅ 端点名称: {specific_endpoint['name']}")
            print(f"✅ URL: {specific_endpoint['url']}")
            print(f"✅ 参数: {specific_endpoint.get('params', {})}")
        else:
            print("\n" + "=" * 60)
            print("😞 未找到可用的API端点")
            print("=" * 60)
            print("💡 建议:")
            print("   1. 检查东方财富网站是否有新的API文档")
            print("   2. 尝试使用浏览器开发者工具分析网站请求")
            print("   3. 考虑暂时禁用东方财富数据源")

if __name__ == "__main__":
    main()
