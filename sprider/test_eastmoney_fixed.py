# -*- coding: utf-8 -*-
"""
测试修复后的东方财富API
"""

import sys
import os
import asyncio
from datetime import datetime
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sync_eastmoney():
    """测试同步版本的东方财富获取"""
    print("🧪 测试同步版本东方财富获取")
    print("=" * 60)
    
    try:
        from ths import get_eastmoney_news
        
        print("🔄 获取东方财富新闻...")
        news_list = get_eastmoney_news()
        
        if news_list:
            print(f"✅ 成功获取 {len(news_list)} 条东方财富新闻")
            
            # 显示前3条新闻
            print(f"\n📰 前3条新闻:")
            for i, news in enumerate(news_list[:3], 1):
                time_str = news['publish_time'].strftime('%H:%M:%S')
                print(f"   {i}. [{time_str}] {news['title'][:50]}...")
                if 'raw_data' in news and news['raw_data']:
                    print(f"      原始字段: {list(news['raw_data'].keys())}")
            
            return True
        else:
            print("❌ 未获取到东方财富新闻")
            return False
            
    except Exception as e:
        print(f"❌ 同步版本东方财富测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_eastmoney():
    """测试异步版本的东方财富获取"""
    print("\n🧪 测试异步版本东方财富获取")
    print("=" * 60)
    
    try:
        from ths_async import get_eastmoney_news_async
        import aiohttp
        
        print("🔄 异步获取东方财富新闻...")
        
        async with aiohttp.ClientSession() as session:
            news_list = await get_eastmoney_news_async(session)
        
        if news_list:
            print(f"✅ 异步成功获取 {len(news_list)} 条东方财富新闻")
            
            # 显示前3条新闻
            print(f"\n📰 前3条新闻:")
            for i, news in enumerate(news_list[:3], 1):
                time_str = news['publish_time'].strftime('%H:%M:%S')
                print(f"   {i}. [{time_str}] {news['title'][:50]}...")
                if 'raw_data' in news and news['raw_data']:
                    print(f"      原始字段: {list(news['raw_data'].keys())}")
            
            return True
        else:
            print("❌ 异步未获取到东方财富新闻")
            return False
            
    except Exception as e:
        print(f"❌ 异步版本东方财富测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_eastmoney_config():
    """测试东方财富配置"""
    print("\n🧪 测试东方财富配置")
    print("=" * 60)
    
    try:
        from config import NEWS_SOURCES
        
        eastmoney_config = NEWS_SOURCES.get('eastmoney', {})
        
        print(f"📋 东方财富配置:")
        print(f"   启用状态: {eastmoney_config.get('enabled', False)}")
        print(f"   API地址: {eastmoney_config.get('url', 'N/A')}")
        print(f"   参数: {eastmoney_config.get('params', {})}")
        
        if eastmoney_config.get('enabled', False):
            print("✅ 东方财富已启用")
            return True
        else:
            print("❌ 东方财富未启用")
            return False
            
    except Exception as e:
        print(f"❌ 东方财富配置测试失败: {e}")
        return False

def test_all_sources():
    """测试所有新闻源"""
    print("\n🧪 测试所有新闻源获取")
    print("=" * 60)
    
    try:
        from ths import get_all_news
        
        print("🔄 获取所有新闻源...")
        all_news = get_all_news()
        
        if all_news:
            print(f"✅ 成功获取 {len(all_news)} 条新闻")
            
            # 统计各数据源
            sources = {}
            for news in all_news:
                source = news['source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"\n📊 数据源分布:")
            for source, count in sources.items():
                print(f"   {source}: {count} 条")
            
            # 检查是否包含东方财富
            if '东方财富' in sources:
                print(f"✅ 东方财富数据源正常工作: {sources['东方财富']} 条")
                return True
            else:
                print("❌ 东方财富数据源未工作")
                return False
        else:
            print("❌ 未获取到任何新闻")
            return False
            
    except Exception as e:
        print(f"❌ 所有新闻源测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_all_sources():
    """测试异步版本所有新闻源"""
    print("\n🧪 测试异步版本所有新闻源")
    print("=" * 60)
    
    try:
        from ths_async import get_all_news_async
        
        print("🔄 异步获取所有新闻源...")
        all_news = await get_all_news_async()
        
        if all_news:
            print(f"✅ 异步成功获取 {len(all_news)} 条新闻")
            
            # 统计各数据源
            sources = {}
            for news in all_news:
                source = news['source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"\n📊 异步数据源分布:")
            for source, count in sources.items():
                print(f"   {source}: {count} 条")
            
            # 检查是否包含东方财富
            if '东方财富' in sources:
                print(f"✅ 异步东方财富数据源正常工作: {sources['东方财富']} 条")
                return True
            else:
                print("❌ 异步东方财富数据源未工作")
                return False
        else:
            print("❌ 异步未获取到任何新闻")
            return False
            
    except Exception as e:
        print(f"❌ 异步所有新闻源测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 东方财富API修复验证测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 验证根据对话历史修复的东方财富API")
    
    # 执行各项测试
    tests = [
        ("东方财富配置检查", test_eastmoney_config),
        ("同步版本东方财富", test_sync_eastmoney),
        ("异步版本东方财富", test_async_eastmoney),
        ("同步版本所有源", test_all_sources),
        ("异步版本所有源", test_async_all_sources)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 东方财富修复测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 东方财富API修复成功！")
        print("💡 修复要点:")
        print("   1. ✅ 恢复原始API端点")
        print("   2. ✅ 增强JSONP格式处理")
        print("   3. ✅ 改进错误处理机制")
        print("   4. ✅ 统一同步和异步版本")
        print("   5. ✅ 保留原始数据用于股票检测")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")
        if passed >= 3:
            print("💡 部分功能已恢复，可以继续使用")
        else:
            print("💡 需要进一步调试API问题")

if __name__ == "__main__":
    asyncio.run(main())
