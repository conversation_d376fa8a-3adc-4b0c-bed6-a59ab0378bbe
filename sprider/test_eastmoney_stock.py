# -*- coding: utf-8 -*-
"""
测试东方财富股票新闻功能
"""

import sys
import os
import asyncio
from datetime import datetime
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_eastmoney_stock_config():
    """测试东方财富股票新闻配置"""
    print("🧪 测试东方财富股票新闻配置")
    print("=" * 60)
    
    try:
        from config import NEWS_SOURCES
        
        if 'eastmoney_stock' not in NEWS_SOURCES:
            print("❌ 东方财富股票配置不存在")
            return False
        
        config = NEWS_SOURCES['eastmoney_stock']
        
        print(f"📋 东方财富股票配置:")
        print(f"   名称: {config.get('name', 'N/A')}")
        print(f"   启用状态: {config.get('enabled', False)}")
        print(f"   API地址: {config.get('url', 'N/A')}")
        print(f"   参数: {config.get('params', {})}")
        
        # 验证URL是否正确
        expected_url = 'https://newsapi.eastmoney.com/kuaixun/v1/getlist_103_ajaxResult_20_1_.html'
        if config.get('url') == expected_url:
            print("✅ API地址配置正确")
        else:
            print(f"❌ API地址配置错误，期望: {expected_url}")
            return False
        
        if config.get('enabled', False):
            print("✅ 东方财富股票新闻已启用")
            return True
        else:
            print("❌ 东方财富股票新闻未启用")
            return False
            
    except Exception as e:
        print(f"❌ 东方财富股票配置测试失败: {e}")
        return False

def test_sync_eastmoney_stock():
    """测试同步版本的东方财富股票新闻获取"""
    print("\n🧪 测试同步版本东方财富股票新闻获取")
    print("=" * 60)
    
    try:
        from ths import get_eastmoney_stock_news
        
        print("🔄 获取东方财富股票新闻...")
        news_list = get_eastmoney_stock_news()
        
        if news_list:
            print(f"✅ 成功获取 {len(news_list)} 条东方财富股票新闻")
            
            # 显示前3条新闻
            print(f"\n📰 前3条股票新闻:")
            for i, news in enumerate(news_list[:3], 1):
                time_str = news['publish_time'].strftime('%H:%M:%S')
                print(f"   {i}. [{time_str}] {news['title'][:50]}...")
                print(f"      来源: {news['source']}")
                print(f"      URL: {news.get('url', 'N/A')}")
                if 'raw_data' in news and news['raw_data']:
                    print(f"      原始字段: {list(news['raw_data'].keys())[:5]}...")
            
            return True
        else:
            print("❌ 未获取到东方财富股票新闻")
            return False
            
    except Exception as e:
        print(f"❌ 同步版本东方财富股票测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_eastmoney_stock():
    """测试异步版本的东方财富股票新闻获取"""
    print("\n🧪 测试异步版本东方财富股票新闻获取")
    print("=" * 60)
    
    try:
        from ths_async import get_eastmoney_stock_news_async
        import aiohttp
        
        print("🔄 异步获取东方财富股票新闻...")
        
        async with aiohttp.ClientSession() as session:
            news_list = await get_eastmoney_stock_news_async(session)
        
        if news_list:
            print(f"✅ 异步成功获取 {len(news_list)} 条东方财富股票新闻")
            
            # 显示前3条新闻
            print(f"\n📰 前3条股票新闻:")
            for i, news in enumerate(news_list[:3], 1):
                time_str = news['publish_time'].strftime('%H:%M:%S')
                print(f"   {i}. [{time_str}] {news['title'][:50]}...")
                print(f"      来源: {news['source']}")
                print(f"      URL: {news.get('url', 'N/A')}")
            
            return True
        else:
            print("❌ 异步未获取到东方财富股票新闻")
            return False
            
    except Exception as e:
        print(f"❌ 异步版本东方财富股票测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_news_detection():
    """测试股票新闻检测"""
    print("\n🧪 测试股票新闻检测")
    print("=" * 60)
    
    try:
        from ths import is_stock_news
        
        # 创建东方财富股票新闻测试数据
        test_news = {
            'news_id': 'em_stock_test_001',
            'source': '东方财富股票',
            'title': '理想汽车回应理想i8与乘龙卡车安全性碰撞测试',
            'content': '理想汽车微博发布《关于理想i8安全性碰撞测试的说明》',
            'digest': '理想汽车回应碰撞测试',
            'publish_time': datetime.now(),
            'url': 'http://finance.eastmoney.com/a/test.html',
            'raw_data': {},
            'stock': []
        }
        
        print(f"📰 测试新闻: {test_news['title']}")
        print(f"📝 新闻来源: {test_news['source']}")
        
        # 检测是否被识别为股票新闻
        is_stock = is_stock_news(test_news)
        
        print(f"🔍 股票检测结果: {'是股票新闻' if is_stock else '不是股票新闻'}")
        
        if is_stock:
            print("✅ 东方财富股票新闻正确识别为股票新闻")
            return True
        else:
            print("❌ 东方财富股票新闻未被识别为股票新闻")
            return False
            
    except Exception as e:
        print(f"❌ 股票新闻检测测试失败: {e}")
        return False

def test_source_priority():
    """测试数据源优先级"""
    print("\n🧪 测试数据源优先级")
    print("=" * 60)
    
    try:
        from ths import get_source_priority
        
        sources = ['同花顺', '财联社', '东方财富股票', '东方财富']
        priorities = [get_source_priority(source) for source in sources]
        
        print("📊 数据源优先级:")
        for source, priority in zip(sources, priorities):
            print(f"   {source}: {priority}")
        
        # 验证优先级顺序
        expected_order = [1, 2, 3, 4]
        if priorities == expected_order:
            print("✅ 数据源优先级设置正确")
            print("💡 优先级顺序: 同花顺 > 财联社 > 东方财富股票 > 东方财富")
            return True
        else:
            print(f"❌ 数据源优先级错误，期望: {expected_order}，实际: {priorities}")
            return False
            
    except Exception as e:
        print(f"❌ 数据源优先级测试失败: {e}")
        return False

def test_all_sources_integration():
    """测试所有数据源集成"""
    print("\n🧪 测试所有数据源集成")
    print("=" * 60)
    
    try:
        from ths import get_all_news
        
        print("🔄 获取所有数据源新闻...")
        all_news = get_all_news()
        
        if all_news:
            print(f"✅ 成功获取 {len(all_news)} 条新闻")
            
            # 统计各数据源
            sources = {}
            for news in all_news:
                source = news['source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"\n📊 数据源分布:")
            for source, count in sources.items():
                print(f"   {source}: {count} 条")
            
            # 检查是否包含东方财富股票
            if '东方财富股票' in sources:
                print(f"✅ 东方财富股票数据源正常工作: {sources['东方财富股票']} 条")
                
                # 显示东方财富股票新闻样例
                stock_news = [news for news in all_news if news['source'] == '东方财富股票']
                if stock_news:
                    print(f"\n📈 东方财富股票新闻样例:")
                    for i, news in enumerate(stock_news[:2], 1):
                        print(f"   {i}. {news['title'][:50]}...")
                
                return True
            else:
                print("❌ 东方财富股票数据源未工作")
                return False
        else:
            print("❌ 未获取到任何新闻")
            return False
            
    except Exception as e:
        print(f"❌ 所有数据源集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_all_sources():
    """测试异步版本所有数据源"""
    print("\n🧪 测试异步版本所有数据源")
    print("=" * 60)
    
    try:
        from ths_async import get_all_news_async
        
        print("🔄 异步获取所有数据源新闻...")
        all_news = await get_all_news_async()
        
        if all_news:
            print(f"✅ 异步成功获取 {len(all_news)} 条新闻")
            
            # 统计各数据源
            sources = {}
            for news in all_news:
                source = news['source']
                sources[source] = sources.get(source, 0) + 1
            
            print(f"\n📊 异步数据源分布:")
            for source, count in sources.items():
                print(f"   {source}: {count} 条")
            
            # 检查是否包含东方财富股票
            if '东方财富股票' in sources:
                print(f"✅ 异步东方财富股票数据源正常工作: {sources['东方财富股票']} 条")
                return True
            else:
                print("❌ 异步东方财富股票数据源未工作")
                return False
        else:
            print("❌ 异步未获取到任何新闻")
            return False
            
    except Exception as e:
        print(f"❌ 异步所有数据源测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 东方财富股票新闻功能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 验证东方财富股票新闻接口和功能")
    
    # 执行各项测试
    tests = [
        ("东方财富股票配置", test_eastmoney_stock_config),
        ("同步版本股票新闻获取", test_sync_eastmoney_stock),
        ("异步版本股票新闻获取", test_async_eastmoney_stock),
        ("股票新闻检测", test_stock_news_detection),
        ("数据源优先级", test_source_priority),
        ("所有数据源集成", test_all_sources_integration),
        ("异步版本所有数据源", test_async_all_sources)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 东方财富股票新闻测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 东方财富股票新闻功能测试全部通过！")
        print("💡 新增功能:")
        print("   1. ✅ 东方财富股票新闻API接口")
        print("   2. ✅ 专门的股票新闻获取函数")
        print("   3. ✅ 自动股票新闻识别")
        print("   4. ✅ 数据源优先级调整")
        print("   5. ✅ 同步和异步版本支持")
        print("   6. ✅ 完整的错误处理机制")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")

if __name__ == "__main__":
    asyncio.run(main())
