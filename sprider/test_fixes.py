# -*- coding: utf-8 -*-
"""
测试修复后的新闻爬虫功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import logging
from ths import get_10jqka_news, get_eastmoney_news, get_cls_news, get_all_news

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_time_consistency():
    """测试时间处理的一致性"""
    print("=" * 60)
    print("测试时间处理一致性")
    print("=" * 60)
    
    # 测试同花顺
    print("\n📰 测试同花顺新闻...")
    ths_news = get_10jqka_news()
    if ths_news:
        print(f"✅ 获取到 {len(ths_news)} 条同花顺新闻")
        sample = ths_news[0]
        print(f"   时间类型: {type(sample['publish_time'])}")
        print(f"   时间值: {sample['publish_time']}")
        print(f"   标题: {sample['title'][:50]}...")
    else:
        print("❌ 未获取到同花顺新闻")
    
    # 测试东方财富
    print("\n📰 测试东方财富新闻...")
    em_news = get_eastmoney_news()
    if em_news:
        print(f"✅ 获取到 {len(em_news)} 条东方财富新闻")
        sample = em_news[0]
        print(f"   时间类型: {type(sample['publish_time'])}")
        print(f"   时间值: {sample['publish_time']}")
        print(f"   标题: {sample['title'][:50]}...")
    else:
        print("❌ 未获取到东方财富新闻")
    
    # 测试财联社
    print("\n📰 测试财联社新闻...")
    cls_news = get_cls_news()
    if cls_news:
        print(f"✅ 获取到 {len(cls_news)} 条财联社新闻")
        sample = cls_news[0]
        print(f"   时间类型: {type(sample['publish_time'])}")
        print(f"   时间值: {sample['publish_time']}")
        print(f"   标题: {sample['title'][:50]}...")
    else:
        print("❌ 未获取到财联社新闻")
    
    return ths_news, em_news, cls_news

def test_sorting():
    """测试新闻排序功能"""
    print("\n" + "=" * 60)
    print("测试新闻排序功能")
    print("=" * 60)
    
    try:
        all_news = get_all_news()
        if all_news:
            print(f"✅ 成功获取并排序 {len(all_news)} 条新闻")
            
            # 检查前5条新闻的时间
            print("\n📅 前5条新闻时间检查:")
            for i, news in enumerate(all_news[:5], 1):
                print(f"   {i}. {news['source']} - {news['publish_time']} - {news['title'][:40]}...")
            
            # 验证排序是否正确（时间递减）
            is_sorted = True
            for i in range(len(all_news) - 1):
                if all_news[i]['publish_time'] < all_news[i + 1]['publish_time']:
                    is_sorted = False
                    break
            
            if is_sorted:
                print("✅ 新闻排序正确（按时间降序）")
            else:
                print("⚠️ 新闻排序可能有问题")
                
        else:
            print("❌ 未获取到任何新闻")
            
    except Exception as e:
        print(f"❌ 排序测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_data_structure():
    """测试数据结构完整性"""
    print("\n" + "=" * 60)
    print("测试数据结构完整性")
    print("=" * 60)
    
    all_news = get_all_news()
    if not all_news:
        print("❌ 无新闻数据可测试")
        return
    
    required_fields = ['news_id', 'source', 'title', 'publish_time']
    
    for i, news in enumerate(all_news[:3], 1):  # 测试前3条
        print(f"\n📰 新闻 {i} 数据结构检查:")
        print(f"   来源: {news['source']}")
        
        missing_fields = []
        for field in required_fields:
            if field not in news:
                missing_fields.append(field)
            else:
                print(f"   ✅ {field}: {str(news[field])[:50]}...")
        
        if missing_fields:
            print(f"   ❌ 缺少字段: {missing_fields}")
        else:
            print("   ✅ 数据结构完整")

def main():
    """主测试函数"""
    print("🧪 新闻爬虫修复测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试时间一致性
    ths_news, em_news, cls_news = test_time_consistency()
    
    # 测试排序功能
    test_sorting()
    
    # 测试数据结构
    test_data_structure()
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("=" * 60)
    
    sources_working = []
    if ths_news:
        sources_working.append("同花顺")
    if em_news:
        sources_working.append("东方财富")
    if cls_news:
        sources_working.append("财联社")
    
    print(f"✅ 工作正常的数据源: {', '.join(sources_working)}")
    print(f"📊 数据源成功率: {len(sources_working)}/3")
    
    if len(sources_working) == 3:
        print("\n🎉 所有修复都成功！系统运行正常")
    elif len(sources_working) > 0:
        print(f"\n⚠️ 部分数据源工作正常，建议检查失败的源")
    else:
        print("\n❌ 所有数据源都有问题，需要进一步调试")

if __name__ == "__main__":
    main()
