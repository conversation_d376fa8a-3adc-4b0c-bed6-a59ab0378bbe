# -*- coding: utf-8 -*-
"""
测试 schedule 库的定时执行功能
"""

import schedule
import time
import signal
import sys
from datetime import datetime

# 全局变量
running = True
execution_count = 0

def signal_handler(signum, frame):
    """信号处理函数"""
    global running
    print(f"\n接收到信号 {signum}，准备停止程序...")
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def test_task():
    """测试任务函数"""
    global execution_count
    execution_count += 1
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"📅 第 {execution_count} 次执行 - {current_time}")
    print(f"   🔄 模拟爬虫任务执行...")
    
    # 模拟任务执行时间
    time.sleep(2)
    
    print(f"   ✅ 第 {execution_count} 次任务完成")
    print(f"   😴 等待下次调度...")

def test_schedule_basic():
    """测试基本的 schedule 功能"""
    print("🧪 测试基本 schedule 功能")
    print("=" * 50)
    
    # 设置每5秒执行一次
    schedule.every(5).seconds.do(test_task)
    
    print("⏰ 设置每5秒执行一次任务")
    print("💡 程序将运行15秒后自动停止")
    print("🔄 也可以按 Ctrl+C 手动停止")
    
    # 立即执行一次
    print("\n🚀 立即执行首次任务...")
    test_task()
    
    start_time = time.time()
    max_run_time = 15  # 最大运行15秒
    
    try:
        print("\n⏰ 开始定时调度...")
        while running and (time.time() - start_time) < max_run_time:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n收到键盘中断信号")
    finally:
        schedule.clear()
        print(f"\n🛑 测试完成，总共执行了 {execution_count} 次任务")

def test_schedule_advanced():
    """测试高级 schedule 功能"""
    print("\n" + "=" * 50)
    print("🧪 测试高级 schedule 功能")
    print("=" * 50)
    
    def task_a():
        print(f"🅰️ 任务A执行 - {datetime.now().strftime('%H:%M:%S')}")
    
    def task_b():
        print(f"🅱️ 任务B执行 - {datetime.now().strftime('%H:%M:%S')}")
    
    def task_c():
        print(f"🅲️ 任务C执行 - {datetime.now().strftime('%H:%M:%S')}")
    
    # 设置不同的调度策略
    schedule.every(3).seconds.do(task_a)
    schedule.every(5).seconds.do(task_b)
    schedule.every(7).seconds.do(task_c)
    
    print("⏰ 设置多个任务:")
    print("   - 任务A: 每3秒执行")
    print("   - 任务B: 每5秒执行")
    print("   - 任务C: 每7秒执行")
    print("💡 程序将运行20秒后自动停止")
    
    start_time = time.time()
    max_run_time = 20
    
    try:
        while (time.time() - start_time) < max_run_time:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n收到键盘中断信号")
    finally:
        schedule.clear()
        print("\n🛑 高级测试完成")

def test_schedule_features():
    """测试 schedule 的各种特性"""
    print("\n" + "=" * 50)
    print("🧪 测试 schedule 特性")
    print("=" * 50)
    
    def feature_task(task_name):
        print(f"🔧 {task_name} - {datetime.now().strftime('%H:%M:%S')}")
    
    # 展示各种调度方式
    print("📋 schedule 支持的调度方式:")
    print("   - schedule.every(10).seconds.do(job)")
    print("   - schedule.every(10).minutes.do(job)")
    print("   - schedule.every().hour.do(job)")
    print("   - schedule.every().day.at('10:30').do(job)")
    print("   - schedule.every().monday.do(job)")
    print("   - schedule.every().wednesday.at('13:15').do(job)")
    
    # 测试一些特性
    schedule.every(2).seconds.do(feature_task, "每2秒任务")
    schedule.every(4).seconds.do(feature_task, "每4秒任务")
    
    print("\n⏰ 运行特性测试10秒...")
    
    start_time = time.time()
    max_run_time = 10
    
    try:
        while (time.time() - start_time) < max_run_time:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n收到键盘中断信号")
    finally:
        schedule.clear()
        print("\n🛑 特性测试完成")

def show_schedule_info():
    """显示 schedule 库信息"""
    print("\n" + "=" * 50)
    print("📚 schedule 库信息")
    print("=" * 50)

    try:
        version = getattr(schedule, '__version__', '未知版本')
        print(f"📦 schedule 版本: {version}")
    except:
        print("📦 schedule 版本: 已安装")

    print("🔗 官方文档: https://schedule.readthedocs.io/")
    print("💡 主要优势:")
    print("   - 简单易用的API")
    print("   - 支持多种时间单位")
    print("   - 支持复杂的调度策略")
    print("   - 轻量级，无外部依赖")
    print("   - 人性化的语法")

def main():
    """主测试函数"""
    global running, execution_count
    
    print("🚀 schedule 库功能测试套件")
    print(f"📅 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 显示库信息
    show_schedule_info()
    
    # 重置计数器
    execution_count = 0
    running = True
    
    # 执行各项测试
    test_schedule_basic()
    
    # 重置状态
    execution_count = 0
    running = True
    
    test_schedule_advanced()
    test_schedule_features()
    
    print("\n" + "=" * 50)
    print("🎯 测试总结")
    print("=" * 50)
    print("✅ 基本功能测试: 完成")
    print("✅ 高级功能测试: 完成")
    print("✅ 特性展示测试: 完成")
    
    print("\n💡 在实际项目中的使用:")
    print("   1. 导入: import schedule")
    print("   2. 设置任务: schedule.every(30).seconds.do(your_function)")
    print("   3. 运行循环: schedule.run_pending()")
    print("   4. 清理任务: schedule.clear()")
    
    print(f"\n📅 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
