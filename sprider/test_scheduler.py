# -*- coding: utf-8 -*-
"""
测试定时执行功能
"""

import sys
import os
import time
import threading
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_scheduler_simulation():
    """模拟定时执行功能测试"""
    print("🧪 定时执行功能测试")
    print("=" * 60)
    
    # 模拟运行参数
    SCHEDULE_INTERVAL = 5  # 测试用较短间隔
    MAX_EXECUTIONS = 3     # 最大执行次数
    
    print(f"⏰ 测试间隔: {SCHEDULE_INTERVAL} 秒")
    print(f"🔢 最大执行次数: {MAX_EXECUTIONS}")
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 测试将自动停止，无需手动中断")
    
    execution_count = 0
    
    try:
        while execution_count < MAX_EXECUTIONS:
            execution_count += 1
            start_time = time.time()
            
            print(f"\n📅 第 {execution_count} 次执行 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 模拟爬虫任务执行
            print("   🔄 模拟获取新闻...")
            time.sleep(1)  # 模拟网络请求时间
            
            print("   🗄️ 模拟保存数据库...")
            time.sleep(0.5)  # 模拟数据库操作时间
            
            print("   📱 模拟Telegram推送...")
            time.sleep(0.5)  # 模拟推送时间
            
            # 计算执行时间
            execution_time = time.time() - start_time
            print(f"   ⏱️ 本次执行耗时: {execution_time:.2f} 秒")
            print(f"   ✅ 第 {execution_count} 次执行完成")
            
            # 等待下次执行
            if execution_count < MAX_EXECUTIONS:
                print(f"   😴 等待 {SCHEDULE_INTERVAL} 秒后执行下次任务...")
                time.sleep(SCHEDULE_INTERVAL)
                
    except KeyboardInterrupt:
        print("\n⚠️ 收到键盘中断信号")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
    finally:
        print(f"\n🛑 测试完成")
        print(f"📊 总共执行了 {execution_count} 次任务")
        print(f"📅 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def test_signal_handling():
    """测试信号处理功能"""
    print("\n" + "=" * 60)
    print("🧪 信号处理功能测试")
    print("=" * 60)
    
    print("💡 这个测试需要手动中断（Ctrl+C）来验证信号处理")
    print("⏰ 程序将每2秒输出一次状态，请在几秒后按 Ctrl+C")
    
    running = True
    count = 0
    
    def signal_simulation():
        nonlocal running
        # 模拟5秒后自动停止（避免无限等待）
        time.sleep(10)
        running = False
        print("\n🤖 自动停止信号（模拟Ctrl+C）")
    
    # 启动自动停止线程
    stop_thread = threading.Thread(target=signal_simulation)
    stop_thread.daemon = True
    stop_thread.start()
    
    try:
        while running:
            count += 1
            print(f"⏳ 运行中... 第 {count} 次检查 - {datetime.now().strftime('%H:%M:%S')}")
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n✅ 成功捕获键盘中断信号")
        running = False
    finally:
        print("🛑 信号处理测试完成")

def test_error_handling():
    """测试错误处理功能"""
    print("\n" + "=" * 60)
    print("🧪 错误处理功能测试")
    print("=" * 60)
    
    print("🔄 测试各种错误情况的处理...")
    
    # 测试1: 模拟网络错误
    print("\n📡 测试1: 模拟网络连接错误")
    try:
        # 模拟网络请求失败
        raise ConnectionError("模拟网络连接失败")
    except ConnectionError as e:
        print(f"   ✅ 成功捕获网络错误: {e}")
    
    # 测试2: 模拟数据库错误
    print("\n🗄️ 测试2: 模拟数据库连接错误")
    try:
        # 模拟数据库连接失败
        raise Exception("模拟数据库连接失败")
    except Exception as e:
        print(f"   ✅ 成功捕获数据库错误: {e}")
    
    # 测试3: 模拟数据解析错误
    print("\n📄 测试3: 模拟数据解析错误")
    try:
        # 模拟JSON解析失败
        import json
        json.loads("invalid json")
    except json.JSONDecodeError as e:
        print(f"   ✅ 成功捕获解析错误: {e}")
    
    print("\n✅ 错误处理测试完成")

def main():
    """主测试函数"""
    print("🚀 定时执行功能完整测试套件")
    print(f"📅 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    test_scheduler_simulation()
    test_signal_handling()
    test_error_handling()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    print("✅ 定时执行模拟测试: 完成")
    print("✅ 信号处理测试: 完成")
    print("✅ 错误处理测试: 完成")
    
    print("\n💡 实际使用建议:")
    print("   1. 运行 python ths.py 开始定时爬虫")
    print("   2. 程序将每30秒执行一次")
    print("   3. 按 Ctrl+C 优雅停止程序")
    print("   4. 查看日志文件了解详细执行情况")
    
    print(f"\n📅 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
