# -*- coding: utf-8 -*-
"""
测试增强的股票新闻检测功能（基于同花顺API的stock字段）
"""

import sys
import os
import asyncio
from datetime import datetime
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_stock_field_detection():
    """测试基于同花顺stock字段的检测"""
    print("🧪 测试基于同花顺stock字段的股票检测")
    print("=" * 60)
    
    try:
        from ths import is_stock_news
        
        # 模拟同花顺API返回的数据
        test_cases = [
            {
                'name': '有股票字段的新闻',
                'news_item': {
                    'title': '2025年底北斗产业规模将超6000亿元',
                    'content': '北斗三号全球卫星导航系统建成五周年之际，产业交出亮眼成绩单',
                    'digest': '北斗三号全球卫星导航系统建成五周年之际，产业交出亮眼成绩单',
                    'raw_data': {
                        'id': '3338493',
                        'title': '2025年底北斗产业规模将超6000亿元',
                        'stock': ['002151', '300045'],  # 有股票代码
                        'tag': 'A股',
                        'tagInfo': [{'id': '50000530', 'name': '北斗导航', 'score': '0.804', 'type': '1'}]
                    }
                },
                'expected': True,
                'reason': 'stock字段不为空'
            },
            {
                'name': '无股票字段的新闻',
                'news_item': {
                    'title': '今日天气晴朗，气温适宜',
                    'content': '今天天气晴朗，最高气温25度',
                    'digest': '天气预报',
                    'raw_data': {
                        'id': '3338494',
                        'title': '今日天气晴朗，气温适宜',
                        'stock': [],  # 空的股票字段
                        'tag': '天气',
                        'tagInfo': []
                    }
                },
                'expected': False,
                'reason': 'stock字段为空且无股票相关标签'
            },
            {
                'name': 'A股标签的新闻',
                'news_item': {
                    'title': '市场整体表现良好',
                    'content': '今日市场整体表现良好，投资者情绪乐观',
                    'digest': '市场表现',
                    'raw_data': {
                        'id': '3338495',
                        'title': '市场整体表现良好',
                        'stock': [],  # 空的股票字段
                        'tag': 'A股',  # 但有A股标签
                        'tagInfo': []
                    }
                },
                'expected': True,
                'reason': 'tag字段包含A股'
            },
            {
                'name': '股票相关tagInfo的新闻',
                'news_item': {
                    'title': '新能源汽车行业发展迅速',
                    'content': '新能源汽车行业发展迅速，相关企业受益',
                    'digest': '新能源汽车',
                    'raw_data': {
                        'id': '3338496',
                        'title': '新能源汽车行业发展迅速',
                        'stock': [],
                        'tag': '行业',
                        'tagInfo': [{'name': '新能源汽车', 'type': '1'}, {'name': '上市公司', 'type': '1'}]
                    }
                },
                'expected': True,
                'reason': 'tagInfo包含上市公司'
            },
            {
                'name': '关键词检测的新闻',
                'news_item': {
                    'title': '某公司股价大涨',
                    'content': '某公司股价今日大涨5%',
                    'digest': '股价上涨',
                    'raw_data': None  # 没有原始数据，使用关键词检测
                },
                'expected': True,
                'reason': '标题包含股价关键词'
            }
        ]
        
        print(f"📊 测试用例: {len(test_cases)} 个")
        
        correct_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 测试用例 {i}: {test_case['name']}")
            print(f"   新闻标题: {test_case['news_item']['title']}")
            
            result = is_stock_news(test_case['news_item'])
            expected = test_case['expected']
            is_correct = result == expected
            
            if is_correct:
                correct_count += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"   {status} 预期: {'股票新闻' if expected else '非股票新闻'}")
            print(f"   {status} 结果: {'股票新闻' if result else '非股票新闻'}")
            print(f"   {status} 原因: {test_case['reason']}")
            
            if not is_correct:
                print(f"   ⚠️ 检测错误！")
        
        accuracy = (correct_count / len(test_cases)) * 100
        print(f"\n📊 检测准确率: {correct_count}/{len(test_cases)} ({accuracy:.1f}%)")
        
        if accuracy >= 90:
            print("✅ 增强股票检测功能测试通过")
            return True
        else:
            print("❌ 增强股票检测准确率不足")
            return False
            
    except Exception as e:
        print(f"❌ 增强股票检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_tonghuashun_data():
    """测试真实同花顺数据的股票检测"""
    print("\n🧪 测试真实同花顺数据的股票检测")
    print("=" * 60)
    
    try:
        from ths import get_10jqka_news, is_stock_news
        
        print("🔄 获取真实同花顺新闻数据...")
        tonghuashun_news = get_10jqka_news()
        
        if not tonghuashun_news:
            print("❌ 未获取到同花顺新闻数据")
            return False
        
        print(f"✅ 获取到 {len(tonghuashun_news)} 条同花顺新闻")
        
        # 分析股票新闻
        stock_news = []
        non_stock_news = []
        
        for news in tonghuashun_news:
            if is_stock_news(news):
                stock_news.append(news)
            else:
                non_stock_news.append(news)
        
        stock_ratio = (len(stock_news) / len(tonghuashun_news)) * 100
        
        print(f"\n📊 同花顺新闻分析:")
        print(f"   总新闻数: {len(tonghuashun_news)} 条")
        print(f"   股票新闻: {len(stock_news)} 条")
        print(f"   非股票新闻: {len(non_stock_news)} 条")
        print(f"   股票比例: {stock_ratio:.1f}%")
        
        # 显示股票新闻详情
        if stock_news:
            print(f"\n📈 股票新闻详情:")
            for i, news in enumerate(stock_news[:5], 1):  # 显示前5条
                print(f"   {i}. {news['title']}")
                if 'raw_data' in news and news['raw_data']:
                    raw_data = news['raw_data']
                    if 'stock' in raw_data and raw_data['stock']:
                        print(f"      股票代码: {raw_data['stock']}")
                    if 'tag' in raw_data:
                        print(f"      标签: {raw_data['tag']}")
                    if 'tagInfo' in raw_data and raw_data['tagInfo']:
                        tag_names = [tag.get('name', '') for tag in raw_data['tagInfo']]
                        print(f"      详细标签: {', '.join(tag_names)}")
                print()
        
        # 显示非股票新闻样例
        if non_stock_news:
            print(f"📰 非股票新闻样例:")
            for i, news in enumerate(non_stock_news[:3], 1):  # 显示前3条
                print(f"   {i}. {news['title']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实同花顺数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_stock_detection():
    """测试异步版本的股票检测"""
    print("\n🧪 测试异步版本的股票检测")
    print("=" * 60)
    
    try:
        from ths_async import get_10jqka_news_async, is_stock_news
        import aiohttp
        
        print("🔄 异步获取同花顺新闻数据...")
        
        async with aiohttp.ClientSession() as session:
            tonghuashun_news = await get_10jqka_news_async(session)
        
        if not tonghuashun_news:
            print("❌ 异步未获取到同花顺新闻数据")
            return False
        
        print(f"✅ 异步获取到 {len(tonghuashun_news)} 条同花顺新闻")
        
        # 检测股票新闻
        stock_count = 0
        for news in tonghuashun_news:
            if is_stock_news(news):
                stock_count += 1
        
        stock_ratio = (stock_count / len(tonghuashun_news)) * 100
        
        print(f"📊 异步版本股票检测结果:")
        print(f"   总新闻数: {len(tonghuashun_news)} 条")
        print(f"   股票新闻: {stock_count} 条")
        print(f"   股票比例: {stock_ratio:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步版本股票检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_telegram_sending():
    """测试股票新闻Telegram发送"""
    print("\n🧪 测试股票新闻Telegram发送")
    print("=" * 60)
    
    try:
        from ths import send_stock_news_to_telegram, is_stock_news
        from config import TELEGRAM_STOCK_NEWS_CONFIG
        
        # 检查配置
        print(f"📱 股票Bot Token: {TELEGRAM_STOCK_NEWS_CONFIG['bot_token'][:10]}...")
        print(f"💬 股票Chat ID: {TELEGRAM_STOCK_NEWS_CONFIG['chat_id']}")
        
        # 创建测试股票新闻
        test_stock_news = {
            'news_id': 'enhanced_stock_test_001',
            'source': '同花顺',
            'title': '测试：比亚迪股价大涨5%，新能源汽车概念股集体上涨',
            'content': '今日A股市场，比亚迪股价大涨5%，带动新能源汽车板块集体上涨',
            'digest': '比亚迪股价上涨，新能源汽车板块表现强劲',
            'publish_time': datetime.now(),
            'url': 'https://enhanced-stock-test.com',
            'raw_data': {
                'stock': ['002594'],  # 比亚迪股票代码
                'tag': 'A股',
                'tagInfo': [{'name': '新能源汽车', 'type': '1'}]
            }
        }
        
        # 验证是股票新闻
        if not is_stock_news(test_stock_news):
            print("❌ 测试新闻未被识别为股票新闻")
            return False
        
        print("✅ 测试新闻正确识别为股票新闻")
        
        user_input = input("是否发送测试股票新闻到专用频道？(y/N): ").strip().lower()
        if user_input != 'y':
            print("✅ 跳过实际发送测试")
            return True
        
        print("🔄 发送测试股票新闻...")
        result = send_stock_news_to_telegram(test_stock_news)
        
        if result:
            print("✅ 股票新闻发送测试成功")
            return True
        else:
            print("❌ 股票新闻发送测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 股票新闻发送测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 增强股票新闻检测功能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("💡 基于同花顺API的stock字段进行精确检测")
    
    # 执行各项测试
    tests = [
        ("基于stock字段的检测", test_stock_field_detection),
        ("真实同花顺数据检测", test_real_tonghuashun_data),
        ("异步版本股票检测", test_async_stock_detection),
        ("股票新闻Telegram发送", test_stock_telegram_sending)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 增强股票检测功能测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有增强股票检测功能测试通过！")
        print("💡 增强功能特点:")
        print("   1. ✅ 优先使用同花顺API的stock字段")
        print("   2. ✅ 检查同花顺的tag和tagInfo字段")
        print("   3. ✅ 关键词检测作为备用方案")
        print("   4. ✅ 支持同步和异步版本")
        print("   5. ✅ 精确的股票新闻识别")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")

if __name__ == "__main__":
    asyncio.run(main())
