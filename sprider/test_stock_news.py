# -*- coding: utf-8 -*-
"""
测试股票新闻检测和发送功能
"""

import sys
import os
import asyncio
from datetime import datetime
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_stock_news_detection():
    """测试股票新闻检测功能"""
    print("🧪 测试股票新闻检测功能")
    print("=" * 60)
    
    try:
        from ths import is_stock_news
        
        # 测试新闻数据
        test_news = [
            {
                'title': '比亚迪股价大涨5%，新能源汽车概念股集体上涨',
                'content': '今日A股市场，比亚迪股价大涨5%，带动新能源汽车板块集体上涨',
                'digest': '比亚迪股价上涨',
                'expected': True,
                'reason': '包含股价、A股、板块等关键词'
            },
            {
                'title': '央行降准0.5个百分点，释放流动性约1万亿元',
                'content': '中国人民银行决定下调存款准备金率0.5个百分点',
                'digest': '央行降准政策',
                'expected': False,
                'reason': '主要是货币政策新闻，不直接涉及股票'
            },
            {
                'title': '腾讯控股发布三季度财报，净利润同比增长39%',
                'content': '腾讯控股公布第三季度业绩，净利润达到400亿元',
                'digest': '腾讯财报业绩',
                'expected': True,
                'reason': '包含财报、净利润等关键词'
            },
            {
                'title': '上证指数收盘上涨1.2%，创业板指涨幅达2.1%',
                'content': '今日沪深两市收盘，上证指数上涨1.2%，深证成指上涨1.5%',
                'digest': '股指上涨',
                'expected': True,
                'reason': '包含上证指数、创业板指等关键词'
            },
            {
                'title': '美联储加息25个基点，符合市场预期',
                'content': '美联储宣布加息25个基点，联邦基金利率上调至5.25%-5.5%',
                'digest': '美联储加息',
                'expected': False,
                'reason': '主要是国际货币政策，不直接涉及股票'
            },
            {
                'title': '贵州茅台(600519)股价突破2000元大关',
                'content': '贵州茅台股价今日突破2000元，创历史新高',
                'digest': '茅台股价新高',
                'expected': True,
                'reason': '包含股票代码600519和股价'
            },
            {
                'title': '北向资金净流入50亿元，外资持续看好A股',
                'content': '今日北向资金净流入50亿元，外资机构持续看好A股市场',
                'digest': '北向资金流入',
                'expected': True,
                'reason': '包含北向资金、A股等关键词'
            },
            {
                'title': '今日天气晴朗，气温适宜',
                'content': '今天天气晴朗，最高气温25度，适合户外活动',
                'digest': '天气预报',
                'expected': False,
                'reason': '天气新闻，与股票无关'
            }
        ]
        
        print(f"📊 测试样本: {len(test_news)} 条新闻")
        
        correct_count = 0
        total_count = len(test_news)
        
        for i, news in enumerate(test_news, 1):
            result = is_stock_news(news)
            expected = news['expected']
            is_correct = result == expected
            
            if is_correct:
                correct_count += 1
                status = "✅"
            else:
                status = "❌"
            
            print(f"\n{status} 测试 {i}: {news['title'][:40]}...")
            print(f"   预期: {'股票新闻' if expected else '非股票新闻'}")
            print(f"   结果: {'股票新闻' if result else '非股票新闻'}")
            print(f"   原因: {news['reason']}")
            
            if not is_correct:
                print(f"   ⚠️ 检测错误！")
        
        accuracy = (correct_count / total_count) * 100
        print(f"\n📊 检测准确率: {correct_count}/{total_count} ({accuracy:.1f}%)")
        
        if accuracy >= 80:
            print("✅ 股票新闻检测功能测试通过")
            return True
        else:
            print("❌ 股票新闻检测准确率不足")
            return False
            
    except Exception as e:
        print(f"❌ 股票新闻检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_telegram_config():
    """测试股票Telegram配置"""
    print("\n🧪 测试股票Telegram配置")
    print("=" * 60)
    
    try:
        from config import TELEGRAM_STOCK_NEWS_CONFIG
        
        # 检查配置完整性
        required_keys = ['bot_token', 'chat_id']
        missing_keys = []
        
        for key in required_keys:
            if key not in TELEGRAM_STOCK_NEWS_CONFIG or not TELEGRAM_STOCK_NEWS_CONFIG[key]:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 股票Telegram配置缺失: {missing_keys}")
            return False
        
        print("✅ 股票Telegram配置完整")
        
        # 检查配置格式
        bot_token = TELEGRAM_STOCK_NEWS_CONFIG['bot_token']
        chat_id = TELEGRAM_STOCK_NEWS_CONFIG['chat_id']
        
        print(f"📱 Bot Token: {bot_token[:10]}...{bot_token[-10:]}")
        print(f"💬 Chat ID: {chat_id}")
        
        if not bot_token.count(':') == 1:
            print("⚠️ bot_token格式可能不正确")
        else:
            print("✅ bot_token格式正确")
        
        try:
            int(chat_id)
            print("✅ chat_id格式正确")
        except ValueError:
            print("⚠️ chat_id格式可能不正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票Telegram配置测试失败: {e}")
        return False

def test_stock_telegram_sending():
    """测试股票Telegram发送功能"""
    print("\n🧪 测试股票Telegram发送功能")
    print("=" * 60)
    
    try:
        from ths import send_stock_news_to_telegram
        
        # 创建测试股票新闻
        test_stock_news = {
            'news_id': 'stock_test_001',
            'source': '股票测试',
            'title': '测试股票新闻：比亚迪股价大涨5%',
            'content': '这是一条测试股票新闻，用于验证股票频道发送功能',
            'digest': '比亚迪股价测试',
            'publish_time': datetime.now(),
            'url': 'https://stock-test.com'
        }
        
        print("⚠️ 注意: 这将发送测试消息到股票Telegram频道")
        print("💡 如果不想实际发送，请跳过此测试")
        
        user_input = input("是否继续发送股票测试消息？(y/N): ").strip().lower()
        if user_input != 'y':
            print("✅ 跳过实际发送测试")
            return True
        
        print("🔄 发送股票测试消息...")
        result = send_stock_news_to_telegram(test_stock_news)
        
        if result:
            print("✅ 股票Telegram发送测试成功")
            return True
        else:
            print("❌ 股票Telegram发送测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 股票Telegram发送测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_stock_functions():
    """测试异步版本的股票功能"""
    print("\n🧪 测试异步版本股票功能")
    print("=" * 60)
    
    try:
        from ths_async import is_stock_news, send_stock_news_to_telegram_sync
        
        # 测试异步版本的股票检测
        test_news = {
            'title': '腾讯控股股价上涨3%，游戏业务表现强劲',
            'content': '腾讯控股今日股价上涨3%，主要受游戏业务强劲表现推动',
            'digest': '腾讯股价上涨'
        }
        
        is_stock = is_stock_news(test_news)
        print(f"📰 测试新闻: {test_news['title']}")
        print(f"🔍 检测结果: {'股票新闻' if is_stock else '非股票新闻'}")
        
        if is_stock:
            print("✅ 异步版本股票检测正常")
            
            # 测试发送功能
            user_input = input("是否测试异步版本股票发送？(y/N): ").strip().lower()
            if user_input == 'y':
                test_news['publish_time'] = datetime.now()
                result = send_stock_news_to_telegram_sync(test_news)
                if result:
                    print("✅ 异步版本股票发送测试成功")
                else:
                    print("❌ 异步版本股票发送测试失败")
                return result
            else:
                print("✅ 跳过异步版本发送测试")
                return True
        else:
            print("❌ 异步版本股票检测失败")
            return False
            
    except Exception as e:
        print(f"❌ 异步版本股票功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_news_stock_detection():
    """测试真实新闻的股票检测"""
    print("\n🧪 测试真实新闻股票检测")
    print("=" * 60)
    
    try:
        from ths import get_all_news, is_stock_news
        
        print("🔄 获取真实新闻数据...")
        all_news = get_all_news()
        
        if not all_news:
            print("❌ 未获取到真实新闻数据")
            return False
        
        print(f"✅ 获取到 {len(all_news)} 条真实新闻")
        
        # 检测股票新闻
        stock_news = []
        for news in all_news:
            if is_stock_news(news):
                stock_news.append(news)
        
        stock_ratio = (len(stock_news) / len(all_news)) * 100
        
        print(f"📊 股票新闻统计:")
        print(f"   总新闻数: {len(all_news)} 条")
        print(f"   股票新闻: {len(stock_news)} 条")
        print(f"   股票比例: {stock_ratio:.1f}%")
        
        # 显示前3条股票新闻
        if stock_news:
            print(f"\n📈 前3条股票新闻:")
            for i, news in enumerate(stock_news[:3], 1):
                print(f"   {i}. {news['source']} - {news['title'][:50]}...")
        else:
            print("\n📝 当前没有检测到股票新闻")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实新闻股票检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 股票新闻功能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    tests = [
        ("股票新闻检测功能", test_stock_news_detection),
        ("股票Telegram配置", test_stock_telegram_config),
        ("股票Telegram发送功能", test_stock_telegram_sending),
        ("异步版本股票功能", test_async_stock_functions),
        ("真实新闻股票检测", test_real_news_stock_detection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 开始测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 股票新闻功能测试结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有股票新闻功能测试通过！")
        print("💡 新增功能:")
        print("   1. ✅ 智能股票新闻检测")
        print("   2. ✅ 双频道Telegram推送")
        print("   3. ✅ 同步和异步版本支持")
        print("   4. ✅ 丰富的股票关键词库")
        print("   5. ✅ 完整的错误处理机制")
    else:
        print(f"\n⚠️ 还有 {total-passed} 项测试未通过")
        print("💡 请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())
