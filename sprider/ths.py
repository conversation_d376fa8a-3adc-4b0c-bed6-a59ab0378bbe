import requests
import time
import os
import json
import mysql.connector
from mysql.connector import Error
from datetime import datetime
import logging
import signal
import sys
import schedule
import hashlib
import re
from difflib import SequenceMatcher
from config import TELEGRAM_CONFIG, TELEGRAM_STOCK_NEWS_CONFIG, DB_CONFIG, NEWS_SOURCES, COMMON_HEADERS, SETTINGS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(SETTINGS['log_file'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 定时执行配置
SCHEDULE_INTERVAL = 15  # 执行间隔（秒）
running = True  # 控制程序运行状态
execution_count = 0  # 执行次数计数器

def signal_handler(signum, frame):
    """信号处理函数，用于优雅地停止程序"""
    global running
    logger.info(f"接收到信号 {signum}，准备停止程序...")
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

# ===================
# 新闻去重相关函数

def clean_text(text):
    """清理文本，用于去重比较"""
    if not text:
        return ""

    # 转换为小写
    text = text.lower()

    # 移除标点符号和特殊字符
    text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)

    # 移除多余空格
    text = re.sub(r'\s+', ' ', text).strip()

    return text

def calculate_similarity(text1, text2):
    """计算两个文本的相似度"""
    if not text1 or not text2:
        return 0.0

    # 清理文本
    clean1 = clean_text(text1)
    clean2 = clean_text(text2)

    if not clean1 or not clean2:
        return 0.0

    # 使用SequenceMatcher计算相似度
    similarity = SequenceMatcher(None, clean1, clean2).ratio()
    return similarity

def generate_content_hash(title, content="", digest=""):
    """生成新闻内容的哈希值"""
    # 合并标题、内容和摘要
    combined_text = f"{title} {content} {digest}"

    # 清理文本
    clean_combined = clean_text(combined_text)

    # 生成MD5哈希
    content_hash = hashlib.md5(clean_combined.encode('utf-8')).hexdigest()
    return content_hash

def is_duplicate_news(news_item, existing_news_list, similarity_threshold=0.85):
    """检查新闻是否与已有新闻重复，返回重复的新闻索引（如果有）"""
    current_title = news_item.get('title', '')
    current_content = news_item.get('content', '')
    current_digest = news_item.get('digest', '')

    for i, existing_news in enumerate(existing_news_list):
        existing_title = existing_news.get('title', '')
        existing_content = existing_news.get('content', '')
        existing_digest = existing_news.get('digest', '')

        # 计算标题相似度
        title_similarity = calculate_similarity(current_title, existing_title)

        # 如果标题相似度很高，认为是重复
        if title_similarity >= similarity_threshold:
            logger.debug(f"发现重复新闻（标题相似度: {title_similarity:.2f}）: {current_title[:50]}...")
            return i

        # 如果有内容，也比较内容相似度
        if current_content and existing_content:
            content_similarity = calculate_similarity(current_content, existing_content)
            if content_similarity >= similarity_threshold:
                logger.debug(f"发现重复新闻（内容相似度: {content_similarity:.2f}）: {current_title[:50]}...")
                return i

        # 如果有摘要，也比较摘要相似度
        if current_digest and existing_digest:
            digest_similarity = calculate_similarity(current_digest, existing_digest)
            if digest_similarity >= similarity_threshold:
                logger.debug(f"发现重复新闻（摘要相似度: {digest_similarity:.2f}）: {current_title[:50]}...")
                return i

    return -1  # 没有重复

# 注释：已改为按时间优先去重，不再使用数据源优先级
# def get_source_priority(source):
#     """获取数据源优先级，数字越小优先级越高"""
#     priority_map = {
#         '同花顺': 1,
#         '财联社': 2,
#         '东方财富股票': 3,  # 东方财富股票新闻优先级较高
#         '东方财富': 4
#     }
#     return priority_map.get(source, 999)  # 未知来源优先级最低

def deduplicate_news_list(news_list):
    """对新闻列表进行去重，按时间优先排序（保留时间最早的新闻）"""
    if not news_list:
        return []

    deduplicated_list = []
    duplicate_count = 0

    for news_item in news_list:
        duplicate_index = is_duplicate_news(news_item, deduplicated_list)

        if duplicate_index == -1:
            # 没有重复，直接添加
            news_item['content_hash'] = generate_content_hash(
                news_item.get('title', ''),
                news_item.get('content', ''),
                news_item.get('digest', '')
            )
            deduplicated_list.append(news_item)
        else:
            # 发现重复，比较时间，保留更早的
            existing_news = deduplicated_list[duplicate_index]
            current_time = news_item.get('publish_time')
            existing_time = existing_news.get('publish_time')

            should_replace = False
            reason = ""

            if current_time and existing_time:
                if current_time < existing_time:
                    should_replace = True
                    reason = f"时间更早 ({current_time} < {existing_time})"
                else:
                    reason = f"保留已有的更早新闻 ({existing_time} <= {current_time})"
            elif current_time and not existing_time:
                # 当前有时间，已存在的没有时间，保留当前的
                should_replace = True
                reason = "当前新闻有时间信息"
            elif not current_time and existing_time:
                # 当前没有时间，已存在的有时间，保留已存在的
                reason = "已存在新闻有时间信息"
            else:
                # 都没有时间信息，保留已存在的
                reason = "都无时间信息，保留已有新闻"

            if should_replace:
                # 替换已存在的新闻
                logger.debug(f"替换重复新闻: {news_item['title'][:50]}... 原因: {reason}")
                news_item['content_hash'] = generate_content_hash(
                    news_item.get('title', ''),
                    news_item.get('content', ''),
                    news_item.get('digest', '')
                )
                deduplicated_list[duplicate_index] = news_item
            else:
                # 保持已存在的新闻
                logger.debug(f"保留已有新闻: {existing_news['title'][:50]}... 原因: {reason}")

            duplicate_count += 1

    if duplicate_count > 0:
        logger.info(f"🔄 去重完成: 处理 {duplicate_count} 条重复新闻（按时间优先，保留最早），最终保留 {len(deduplicated_list)} 条")

    return deduplicated_list

# ===================
# 股票新闻检测函数

def is_stock_news(news_item):
    """检测新闻是否包含股票相关内容"""
    # 东方财富股票新闻默认都是股票新闻
    if news_item.get('source') == '东方财富股票':
        return True

    # 优先使用同花顺API的stock字段判断
    if news_item.get('source') == '同花顺' and news_item.get('stock') != []:
        return True
    if news_item.get('source') == '财联社' and news_item.get('stock') != []:
        return True
    return False

# ===================
# JSON文件操作函数

def load_sent_news_ids():
    """从JSON文件加载已发送的新闻ID"""
    json_file = 'last_news1.json'
    try:
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return set(data.get('sent_ids', []))
        else:
            return set()
    except Exception as e:
        logger.error(f"加载已发送新闻ID失败: {e}")
        return set()

def save_sent_news_ids(sent_ids):
    """保存已发送的新闻ID到JSON文件"""
    json_file = 'last_news1.json'
    try:
        data = {'sent_ids': list(sent_ids)}
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"保存已发送新闻ID: {len(sent_ids)} 条")
    except Exception as e:
        logger.error(f"保存已发送新闻ID失败: {e}")

def filter_new_news_by_json(news_list):
    """基于JSON文件过滤出新的新闻"""
    if not news_list:
        return []

    # 加载已发送的新闻ID
    sent_ids = load_sent_news_ids()

    # 过滤出新的新闻
    new_news = []
    for news in news_list:
        news_id = news.get('news_id')
        if news_id and news_id not in sent_ids:
            new_news.append(news)

    logger.info(f"JSON过滤结果: 总新闻 {len(news_list)} 条，新新闻 {len(new_news)} 条，已发送 {len(sent_ids)} 条")
    return new_news

def update_sent_news_ids(news_list):
    """更新已发送的新闻ID列表"""
    if not news_list:
        return

    # 加载现有的已发送ID
    sent_ids = load_sent_news_ids()

    # 添加新发送的ID
    for news in news_list:
        news_id = news.get('news_id')
        if news_id:
            sent_ids.add(news_id)

    # 保持最近的1000条记录，避免文件过大
    if len(sent_ids) > 1000:
        sent_ids = set(list(sent_ids)[-1000:])

    # 保存更新后的ID列表
    save_sent_news_ids(sent_ids)

# ===================
# 数据库操作函数

def create_connection():
    """创建数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        logger.info("数据库连接成功")
        return conn
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def create_news_table(conn):
    """创建新闻表"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS financial_news (
        id INT AUTO_INCREMENT PRIMARY KEY,
        news_id VARCHAR(100) NOT NULL UNIQUE,
        source VARCHAR(50) NOT NULL,
        title TEXT NOT NULL,
        content TEXT,
        digest TEXT,
        publish_time DATETIME NOT NULL,
        url VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_source (source),
        INDEX idx_publish_time (publish_time),
        INDEX idx_news_id (news_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    try:
        cursor = conn.cursor()
        cursor.execute(create_table_sql)
        conn.commit()
        logger.info("新闻表创建/检查成功")
    except Error as e:
        logger.error(f"创建新闻表失败: {e}")

def save_news_to_db(conn, news_list):
    """保存新闻到数据库"""
    if not news_list:
        return 0

    try:
        cursor = conn.cursor()

        # 先查询已存在的news_id
        existing_ids = set()
        if news_list:
            news_ids = [news['news_id'] for news in news_list]
            placeholders = ','.join(['%s'] * len(news_ids))
            check_sql = f"SELECT news_id FROM news WHERE news_id IN ({placeholders})"
            cursor.execute(check_sql, news_ids)
            existing_rows = cursor.fetchall()
            existing_ids = {row[0] for row in existing_rows}

        # 过滤出新的新闻
        new_news = [news for news in news_list if news['news_id'] not in existing_ids]

        if not new_news:
            logger.info("所有新闻都已存在于数据库中，跳过保存")
            return 0

        # 插入新新闻
        insert_sql = """
        INSERT INTO news (news_id, source, title, content, digest, publish_time, url)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """

        data = []
        for news in new_news:
            data.append((
                news['news_id'],
                news['source'],
                news['title'],
                news.get('content', ''),
                news.get('digest', ''),
                news['publish_time'],
                news.get('url', '')
            ))

        cursor.executemany(insert_sql, data)
        conn.commit()
        saved_count = len(new_news)

        if existing_ids:
            logger.info(f"保存新闻: 新增 {saved_count} 条，跳过重复 {len(existing_ids)} 条")
        else:
            logger.info(f"成功保存 {saved_count} 条新闻到数据库")

        return saved_count
    except Error as e:
        logger.error(f"保存新闻到数据库失败: {e}")
        conn.rollback()
        return 0

# ===================
# 新闻获取函数

def get_10jqka_news():
    """获取同花顺7*24快讯"""
    if not NEWS_SOURCES['tonghuashun']['enabled']:
        return []

    config = NEWS_SOURCES['tonghuashun']
    headers = COMMON_HEADERS.copy()
    headers["Referer"] = "https://news.10jqka.com.cn/"

    try:
        response = requests.get(config['url'], params=config['params'], headers=headers, timeout=SETTINGS['request_timeout'])
        response.raise_for_status()
        raw_data = response.json().get("data", {}).get("list", [])

        # 标准化数据格式
        news_list = []
        for item in raw_data:
            news_item = {
                'news_id': f"ths_{item['id']}",
                'source': '同花顺',
                'title': item['title'],
                'content': item.get('content', ''),
                'digest': item.get('digest', ''),
                'publish_time': convert_datetime(item['ctime']),
                'url': item.get('url', ''),
                'raw_data': item,  # 保留原始数据用于Telegram推送
                'stock': item.get('stock', [])
            }
            news_list.append(news_item)

        logger.info(f"获取同花顺新闻 {len(news_list)} 条")
        return news_list
    except Exception as e:
        logger.error(f"获取同花顺数据失败: {e}")
        return []

def get_eastmoney_news():
    """获取东方财富7*24快讯"""
    if not NEWS_SOURCES['eastmoney']['enabled']:
        return []

    config = NEWS_SOURCES['eastmoney']
    params = config['params'].copy()
    params['_'] = str(int(time.time() * 1000))

    headers = COMMON_HEADERS.copy()
    headers["Referer"] = "https://finance.eastmoney.com/"

    try:
        response = requests.get(config['url'], params=params, headers=headers, timeout=SETTINGS['request_timeout'])
        response.raise_for_status()

        # 处理东方财富的特殊响应格式
        text = response.text.strip()

        # 增强的JSONP处理
        original_text = text

        # 处理各种JSONP格式
        if text.startswith('var ajaxResult='):
            text = text[15:].rstrip(';')
        elif text.startswith('callback(') and text.endswith(')'):
            text = text[9:-1]  # 移除callback()包装
        elif text.startswith('jQuery') and '(' in text:
            # 处理jQuery回调格式
            start = text.find('(')
            end = text.rfind(')')
            if start > 0 and end > start:
                text = text[start+1:end]
        elif '(' in text and text.endswith(')'):
            # 通用JSONP格式处理
            start = text.find('(')
            end = text.rfind(')')
            if start > 0 and end > start:
                text = text[start+1:end]

        try:
            data = json.loads(text)
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，尝试其他方法
            logger.warning(f"东方财富JSON解析失败，尝试其他方法: {e}")

            # 尝试查找JSON数据
            import re
            json_match = re.search(r'\{.*\}', original_text, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group())
                except:
                    logger.error(f"东方财富数据解析完全失败, 响应内容: {original_text[:200]}")
                    return []
            else:
                logger.error(f"东方财富未找到JSON数据, 响应内容: {original_text[:200]}")
                return []

        # 提取新闻列表 - 根据实际API响应结构调整
        news_data = []
        if isinstance(data, dict):
            if 'LivesList' in data:
                news_data = data['LivesList']
            elif 'data' in data:
                if isinstance(data['data'], list):
                    news_data = data['data']
                elif isinstance(data['data'], dict) and 'list' in data['data']:
                    news_data = data['data']['list']
            elif 'result' in data and 'data' in data['result']:
                news_data = data['result']['data']
        elif isinstance(data, list):
            news_data = data

        news_list = []
        for item in news_data:
            try:
                # 处理时间格式
                publish_time = datetime.now()  # 默认当前时间

                if 'showtime' in item:
                    try:
                        publish_time = datetime.fromtimestamp(int(item['showtime']))
                    except:
                        pass
                elif 'datetime' in item:
                    try:
                        publish_time = datetime.fromtimestamp(int(item['datetime']))
                    except:
                        pass
                elif 'ptime' in item:
                    try:
                        publish_time = datetime.strptime(item['ptime'], '%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                elif 'time' in item:
                    try:
                        if isinstance(item['time'], (int, float)):
                            publish_time = datetime.fromtimestamp(item['time'])
                        else:
                            publish_time = datetime.strptime(str(item['time']), '%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                # 构建新闻项
                news_item = {
                    'news_id': f"em_{item.get('id', item.get('newsid', item.get('art_id', str(hash(item.get('title', ''))))))}",
                    'source': '东方财富',
                    'title': item.get('title', ''),
                    'content': item.get('content', item.get('digest', '')),
                    'digest': item.get('digest', item.get('description', item.get('title', '')[:100])),
                    'publish_time': publish_time,
                    'url': item.get('url_w', item.get('url', item.get('url_unique', ''))),
                    'raw_data': item
                }

                # 确保必要字段不为空
                if news_item['title']:
                    news_list.append(news_item)

            except Exception as e:
                logger.warning(f"处理东方财富单条新闻失败: {e}")
                continue

        logger.info(f"获取东方财富新闻 {len(news_list)} 条")
        return news_list

    except Exception as e:
        logger.error(f"获取东方财富数据失败: {e}")
        return []

def get_eastmoney_stock_news():
    """获取东方财富股票新闻"""
    config = NEWS_SOURCES['eastmoney_stock']
    if not config['enabled']:
        return []

    url = config['url']
    params = config['params']

    try:
        headers = COMMON_HEADERS.copy()
        headers["Referer"] = "https://finance.eastmoney.com/"

        response = requests.get(url, params=params, headers=headers, timeout=SETTINGS['request_timeout'])
        response.raise_for_status()

        # 处理东方财富的特殊响应格式
        text = response.text.strip()
        original_text = text

        # 增强的JSONP处理
        if text.startswith('var ajaxResult='):
            text = text[15:].rstrip(';')
        elif text.startswith('callback(') and text.endswith(')'):
            text = text[9:-1]  # 移除callback()包装
        elif text.startswith('jQuery') and '(' in text:
            # 处理jQuery回调格式
            start = text.find('(')
            end = text.rfind(')')
            if start > 0 and end > start:
                text = text[start+1:end]
        elif '(' in text and text.endswith(')'):
            # 通用JSONP格式处理
            start = text.find('(')
            end = text.rfind(')')
            if start > 0 and end > start:
                text = text[start+1:end]

        try:
            data = json.loads(text)
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，尝试其他方法
            logger.warning(f"东方财富股票新闻JSON解析失败，尝试其他方法: {e}")

            # 尝试查找JSON数据
            import re
            json_match = re.search(r'\{.*\}', original_text, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group())
                except:
                    logger.error(f"东方财富股票新闻数据解析完全失败, 响应内容: {original_text[:200]}")
                    return []
            else:
                logger.error(f"东方财富股票新闻未找到JSON数据, 响应内容: {original_text[:200]}")
                return []

        # 提取新闻列表
        news_data = []
        if isinstance(data, dict):
            if 'LivesList' in data:
                news_data = data['LivesList']
            elif 'data' in data:
                if isinstance(data['data'], list):
                    news_data = data['data']
                elif isinstance(data['data'], dict) and 'list' in data['data']:
                    news_data = data['data']['list']
            elif 'result' in data and 'data' in data['result']:
                news_data = data['result']['data']
        elif isinstance(data, list):
            news_data = data

        news_list = []
        for item in news_data:
            try:
                # 处理时间格式
                publish_time = datetime.now()  # 默认当前时间

                if 'showtime' in item:
                    try:
                        publish_time = datetime.fromtimestamp(int(item['showtime']))
                    except:
                        pass
                elif 'datetime' in item:
                    try:
                        publish_time = datetime.fromtimestamp(int(item['datetime']))
                    except:
                        pass
                elif 'ptime' in item:
                    try:
                        publish_time = datetime.strptime(item['ptime'], '%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                news_item = {
                    'news_id': f"em_stock_{item.get('id', item.get('newsid', str(hash(item.get('title', '')))))}",
                    'source': '东方财富股票',
                    'title': item.get('title', ''),
                    'content': item.get('content', item.get('digest', '')),
                    'digest': item.get('digest', item.get('title', '')[:100]),
                    'publish_time': publish_time,
                    'url': item.get('url_w', item.get('url', '')),
                    'raw_data': item,
                    'stock': []  # 东方财富股票新闻默认都是股票相关
                }

                if news_item['title']:
                    news_list.append(news_item)

            except Exception as e:
                logger.warning(f"处理东方财富股票新闻单条失败: {e}")
                continue

        logger.info(f"获取东方财富股票新闻 {len(news_list)} 条")
        return news_list

    except Exception as e:
        logger.error(f"获取东方财富股票新闻失败: {e}")
        return []

def get_cls_news():
    """获取财联社7*24快讯 - 使用最新API"""
    if not NEWS_SOURCES['cailianshe']['enabled']:
        return []

    # 使用最新的财联社API端点
    api_url = "https://www.cls.cn/nodeapi/telegraphList"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        "Referer": "https://www.cls.cn/telegraph",
        "Accept": "application/json",
        "X-Requested-With": "XMLHttpRequest"
    }

    params = {
        "refresh_type": "1",  # 1表示获取最新数据
        "rn": "20",  # 获取数量
        "last_time": "0",  # 从最新开始
        "app": "CailianpressWeb",
        "sv": "9.0.0"
    }

    try:
        response = requests.get(api_url, headers=headers, params=params, timeout=SETTINGS['request_timeout'])
        response.raise_for_status()

        # 解析响应数据
        data = response.json()
        if data.get("error") != 0:
            logger.error(f"财联社API返回错误: {data.get('message', '未知错误')}")
            return []

        # 提取新闻列表
        news_list = []
        for item in data.get("data", {}).get("roll_data", []):
            try:
                # 使用API返回的原始时间戳（10位秒级）
                ctime = item.get("ctime")
                if not ctime:
                    continue

                # 内容处理：优先使用content，没有则使用brief
                content = item.get("content", "")
                if not content:
                    content = item.get("brief", "")

                # 重要性判断
                importance = 0
                if "level" in item and item["level"] == "A":
                    importance = 3
                elif "is_top" in item and item["is_top"] == 1:
                    importance = 2

                # 转换为标准格式
                publish_time = datetime.fromtimestamp(int(ctime))

                stock = item.get("stock", [])
                stock = [(s.get("name", ""), s.get('StockID', '')) for s in stock] if stock != [] else []

                news_item = {
                    'news_id': f"cls_{item.get('id', '')}",
                    'source': '财联社',
                    'title': item.get('title', ''),
                    'content': content,
                    'digest': content[:100] if content else item.get('title', '')[:100],
                    'publish_time': publish_time,
                    'url': f"https://www.cls.cn/detail/{item.get('id', '')}",
                    'importance': importance,
                    'raw_data': item,
                    'stock': stock,
                }
                news_list.append(news_item)

            except Exception as e:
                logger.warning(f"处理财联社单条新闻失败: {e}")
                logger.debug(f"问题数据: {json.dumps(item, ensure_ascii=False)[:200]}")
                continue

        logger.info(f"获取财联社新闻 {len(news_list)} 条")
        return news_list

    except Exception as e:
        logger.error(f"获取财联社数据失败: {e}")
        return []


def filter_new_news(news_list):
    """过滤未推送过的新闻"""
    data_file = SETTINGS['data_file']
    if not os.path.exists(data_file):
        return news_list

    try:
        with open(data_file, "r", encoding='utf-8') as f:
            sent_data = json.load(f)
            sent_ids = set(sent_data.get("sent_ids", []))
    except:
        sent_ids = set()

    return [news for news in news_list if news["news_id"] not in sent_ids]


def send_to_telegram(news_item):
    """发送单条新闻到Telegram"""
    # 统一格式化时间
    if isinstance(news_item['publish_time'], datetime):
        time_str = news_item['publish_time'].strftime("%Y-%m-%d %H:%M:%S")
    else:
        # 如果是字符串，直接使用
        time_str = str(news_item['publish_time'])

    message = (
        f"来源：{news_item['source']}\n"
        f"🆕 **{time_str}**\n"
        f"`{news_item['title']}`\n"
        f"{news_item.get('digest', '')}"
    )

    # 如果消息太长，截断处理
    if len(message) > 4000:
        message = message[:3900] + "..."

    api_url = f"https://api.telegram.org/bot{TELEGRAM_CONFIG['bot_token']}/sendMessage"
    payload = {
        "chat_id": TELEGRAM_CONFIG['chat_id'],
        "text": message,
        "parse_mode": "Markdown",
        "disable_web_page_preview": True,
    }

    try:
        response = requests.post(api_url, json=payload, timeout=10)
        result = response.json().get("ok", False)
        if result:
            logger.info(f"Telegram发送成功: {news_item['title'][:30]}...")
        else:
            logger.error(f"Telegram发送失败: {response.json()}")
        return result
    except Exception as e:
        logger.error(f"Telegram发送异常: {e}")
        return False

def send_stock_news_to_telegram(news_item):
    """发送股票新闻到专用Telegram频道"""
    # 统一格式化时间
    if isinstance(news_item['publish_time'], datetime):
        time_str = news_item['publish_time'].strftime("%Y-%m-%d %H:%M:%S")
    else:
        time_str = str(news_item['publish_time'])

    # 构建股票新闻消息（添加股票标识）
    message = (
        f"📈 **股票新闻** 📈\n"
        f"来源：{news_item['source']}\n"
        f"🆕 **{time_str}**\n"
        f"`{news_item['title']}`\n"
        f"{news_item.get('digest', '')}"
        f"\n\n股票: {', '.join([f'{stock}' for stock in news_item['stock']])}"
    )

    # 如果消息太长，截断处理
    if len(message) > 4000:
        message = message[:3900] + "..."

    api_url = f"https://api.telegram.org/bot{TELEGRAM_STOCK_NEWS_CONFIG['bot_token']}/sendMessage"
    payload = {
        "chat_id": TELEGRAM_STOCK_NEWS_CONFIG['chat_id'],
        "text": message,
        "parse_mode": "Markdown",
        "disable_web_page_preview": True,
    }

    try:
        response = requests.post(api_url, json=payload, timeout=10)
        result = response.json().get("ok", False)
        if result:
            logger.info(f"股票新闻Telegram发送成功: {news_item['title'][:30]}...")
        else:
            logger.error(f"股票新闻Telegram发送失败: {response.json()}")
        return result
    except Exception as e:
        logger.error(f"股票新闻Telegram发送异常: {e}")
        return False

def update_sent_records(news_list):
    """更新已发送记录"""
    if not news_list:
        return

    data_file = SETTINGS['data_file']
    try:
        if os.path.exists(data_file):
            with open(data_file, "r", encoding='utf-8') as f:
                data = json.load(f)
        else:
            data = {"sent_ids": []}

        # 添加新ID并限制记录数量
        new_ids = {item["news_id"] for item in news_list}
        max_records = SETTINGS['max_records']
        data["sent_ids"] = list(set(data["sent_ids"]) | new_ids)[-max_records:]

        with open(data_file, "w", encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.info(f"更新发送记录: {len(new_ids)} 条")
    except Exception as e:
        logger.error(f"更新记录失败: {e}")

def convert_datetime(timestamp_str):
    """转换时间戳为datetime对象"""
    timestamp = int(timestamp_str)
    return datetime.fromtimestamp(timestamp)

def get_all_news():
    """获取所有来源的新闻"""
    all_news = []
    original_count = 0

    # 获取同花顺新闻
    ths_news = get_10jqka_news()
    if ths_news:
        logger.info(f"📰 同花顺获取: {len(ths_news)} 条")
        all_news.extend(ths_news)
        original_count += len(ths_news)


    # 获取东方财富新闻
    em_news = get_eastmoney_news()
    if em_news:
        logger.info(f"📰 东方财富获取: {len(em_news)} 条")
        all_news.extend(em_news)
        original_count += len(em_news)

    # 获取东方财富股票新闻
    em_stock_news = get_eastmoney_stock_news()
    if em_stock_news:
        logger.info(f"📰 东方财富股票获取: {len(em_stock_news)} 条")
        all_news.extend(em_stock_news)
        original_count += len(em_stock_news)

    # 获取财联社新闻
    cls_news = get_cls_news()
    if cls_news:
        logger.info(f"📰 财联社获取: {len(cls_news)} 条")
        all_news.extend(cls_news)
        original_count += len(cls_news)

    logger.info(f"📊 原始新闻总数: {original_count} 条")

    # 确保所有新闻的publish_time都是datetime对象
    for news in all_news:
        if not isinstance(news['publish_time'], datetime):
            try:
                # 尝试解析字符串时间
                if isinstance(news['publish_time'], str):
                    news['publish_time'] = datetime.strptime(news['publish_time'], '%Y-%m-%d %H:%M:%S')
                else:
                    # 如果是其他类型，使用当前时间
                    news['publish_time'] = datetime.now()
                    logger.warning(f"新闻时间格式异常，使用当前时间: {news['title'][:30]}")
            except Exception as e:
                news['publish_time'] = datetime.now()
                logger.warning(f"时间解析失败，使用当前时间: {e}")



    # 执行去重处理
    if len(all_news) > 1:
        logger.info("🔄 开始新闻去重处理...")
        all_news = deduplicate_news_list(all_news)

    # 按发布时间排序（最新的在前）
    try:
        all_news.sort(key=lambda x: x['publish_time'], reverse=True)
        logger.info("📅 新闻按时间排序完成")
    except Exception as e:
        logger.error(f"新闻排序失败: {e}")
        # 如果排序失败，至少返回未排序的新闻
        pass

    logger.info(f"✅ 最终新闻总数: {len(all_news)} 条（去重后）")
    return all_news

def execute_crawl_task():
    """执行一次爬虫任务"""
    logger.info("开始执行新闻爬虫任务")

    try:
        # 获取所有新闻数据
        all_news = get_all_news()
        if not all_news:
            logger.info("未获取到新闻数据")
            return True

        # 使用JSON文件过滤新新闻
        new_news = filter_new_news_by_json(all_news)
        if not new_news:
            logger.info("没有新快讯需要推送")
            return True

        logger.info(f"发现 {len(new_news)} 条新快讯需要推送")

        # 只有新新闻才存储到数据库
        if new_news:
            # 创建数据库连接
            conn = create_connection()
            if conn:
                try:
                    # 创建新闻表
                    create_news_table(conn)

                    # 只保存新新闻到数据库
                    saved_count = save_news_to_db(conn, new_news)
                    logger.info(f"保存新新闻到数据库: {saved_count} 条")
                except Exception as e:
                    logger.error(f"数据库操作失败: {e}")
                finally:
                    conn.close()
            else:
                logger.warning("数据库连接失败，但继续推送新闻")

        # 按时间顺序发送（旧→新）
        success_count = 0
        stock_news_count = 0
        telegram_delay = SETTINGS['telegram_delay']

        for news in reversed(new_news):
            if not running:  # 检查是否需要停止
                logger.info("收到停止信号，中断推送")
                break

            logger.info(f"推送: {news['source']} - {news['title'][:30]}...")

            # 检查是否为股票新闻
            is_stock = is_stock_news(news)

            # 发送到主频道
            main_sent = send_to_telegram(news)
            if main_sent:
                success_count += 1
                time.sleep(telegram_delay)  # 避免发送过快被限流
            else:
                time.sleep(telegram_delay * 2)  # 发送失败时等待更长时间

            # 如果是股票新闻且股票频道与主频道不同，则发送到股票频道
            if is_stock:
                logger.info(f"检测到股票新闻，发送到股票频道: {news['title'][:30]}...")
                stock_sent = send_stock_news_to_telegram(news)
                if stock_sent:
                    stock_news_count += 1
                    logger.info(f"股票新闻推送成功")
                else:
                    logger.warning(f"股票新闻推送失败")

                # 股票新闻发送后额外延迟
                time.sleep(telegram_delay)
            

        logger.info(f"成功推送 {success_count}/{len(new_news)} 条新闻到主频道")
        if stock_news_count > 0:
            logger.info(f"成功推送 {stock_news_count} 条股票新闻到股票频道")

        # 更新JSON文件中的已发送记录
        update_sent_news_ids(new_news)
        return True

    except Exception as e:
        logger.error(f"爬虫任务执行异常: {e}")
        return False

def scheduled_crawl_task():
    """被调度的爬虫任务"""
    global execution_count
    execution_count += 1

    start_time = time.time()
    logger.info(f"📅 第 {execution_count} 次执行 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行爬虫任务
    success = execute_crawl_task()

    # 计算执行时间
    execution_time = time.time() - start_time
    logger.info(f"⏱️ 本次执行耗时: {execution_time:.2f} 秒")

    if not success:
        logger.warning("⚠️ 本次执行失败")

    logger.info(f"😴 等待下次调度执行...")

def main():
    """主函数 - 使用 schedule 库定时执行爬虫任务"""
    global running, execution_count
    execution_count = 0

    logger.info("🚀 财经新闻爬虫系统启动")
    logger.info(f"⏰ 执行间隔: {SCHEDULE_INTERVAL} 秒")
    logger.info("💡 按 Ctrl+C 停止程序")
    logger.info("📚 使用 schedule 库进行任务调度")

    # 设置定时任务
    schedule.every(SCHEDULE_INTERVAL).seconds.do(scheduled_crawl_task)

    # 立即执行一次
    logger.info("🔄 立即执行首次任务...")
    scheduled_crawl_task()

    try:
        logger.info("⏰ 开始定时调度...")
        while running:
            # 运行待执行的任务
            schedule.run_pending()
            time.sleep(1)  # 每秒检查一次

    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        logger.info("🛑 财经新闻爬虫系统已停止")
        logger.info(f"📊 总共执行了 {execution_count} 次任务")

        # 清除所有调度任务
        schedule.clear()
        logger.info("🧹 已清除所有调度任务")

if __name__ == "__main__":
    main()