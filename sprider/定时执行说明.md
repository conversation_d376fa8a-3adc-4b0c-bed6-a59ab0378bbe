# 财经新闻爬虫定时执行功能说明

## 🚀 功能概述

财经新闻爬虫系统现已支持定时执行功能，每30秒自动执行一次新闻爬取任务，实现7×24小时不间断监控财经新闻。

## ⏰ 定时执行特性

### 1. 执行间隔
- **默认间隔**: 30秒
- **可配置**: 修改 `ths.py` 中的 `SCHEDULE_INTERVAL` 变量
- **智能等待**: 分段等待，能够快速响应停止信号

### 2. 执行流程
```
启动程序 → 第1次执行 → 等待30秒 → 第2次执行 → 等待30秒 → ...
```

每次执行包含：
1. 🔄 获取新闻数据（同花顺 + 东方财富）
2. 🗄️ 保存到数据库（自动去重）
3. 📱 推送新新闻到Telegram
4. 📊 记录执行统计信息

### 3. 优雅停止
- **键盘中断**: 按 `Ctrl+C` 停止
- **信号处理**: 支持 SIGINT 和 SIGTERM 信号
- **安全退出**: 完成当前任务后再停止
- **资源清理**: 自动关闭数据库连接

## 📋 使用方法

### 1. 启动定时爬虫
```bash
# 进入项目目录
cd sprider

# 启动定时执行
python ths.py
```

### 2. 程序输出示例
```
🚀 财经新闻爬虫系统启动
⏰ 执行间隔: 30 秒
💡 按 Ctrl+C 停止程序
📅 第 1 次执行 - 2025-08-02 18:14:55
开始执行新闻爬虫任务
数据库连接成功
新闻表创建/检查成功
获取同花顺新闻 20 条
获取东方财富新闻 20 条
总共获取新闻 40 条
成功保存 1 条新闻到数据库
发现 1 条新快讯需要推送
推送: 同花顺 - 新疆开行首趟棉花班列...
Telegram发送成功: 新疆开行首趟棉花班列...
成功推送 1/1 条新闻
⏱️ 本次执行耗时: 8.90 秒
😴 等待 30 秒后执行下次任务...
```

### 3. 停止程序
```bash
# 方法1: 键盘中断
Ctrl+C

# 方法2: 发送终止信号（Linux/Mac）
kill -TERM <进程ID>
```

## 🔧 配置选项

### 1. 修改执行间隔
编辑 `ths.py` 文件：
```python
# 定时执行配置
SCHEDULE_INTERVAL = 30  # 修改这个值（秒）
```

常用间隔设置：
- **30秒**: 高频监控（默认）
- **60秒**: 1分钟间隔
- **300秒**: 5分钟间隔
- **600秒**: 10分钟间隔

### 2. 数据源配置
在 `config.py` 中启用/禁用数据源：
```python
NEWS_SOURCES = {
    'tonghuashun': {
        'enabled': True,  # 同花顺
        # ...
    },
    'eastmoney': {
        'enabled': True,  # 东方财富
        # ...
    },
    'cailianshe': {
        'enabled': False,  # 财联社（暂时禁用）
        # ...
    }
}
```

### 3. 日志配置
日志文件位置：`news_crawler.log`
```python
SETTINGS = {
    'log_file': 'news_crawler.log',  # 日志文件名
    # ...
}
```

## 📊 监控和统计

### 1. 实时监控
程序运行时会显示：
- 📅 执行次数和时间
- 📰 获取的新闻数量
- 🗄️ 保存到数据库的数量
- 📱 推送的新闻数量
- ⏱️ 每次执行耗时

### 2. 日志记录
所有操作都会记录到日志文件：
- 执行开始/结束时间
- 数据获取结果
- 数据库操作状态
- Telegram推送结果
- 错误和异常信息

### 3. 查看日志
```bash
# 实时查看日志
tail -f news_crawler.log

# 查看最近100行
tail -100 news_crawler.log

# 搜索特定内容
grep "错误\|异常\|失败" news_crawler.log
```

## 🛠️ 故障排除

### 1. 常见问题

**问题**: 程序启动后没有输出
```bash
# 解决方案: 检查Python环境和依赖
python -c "import ths; print('Import successful')"
pip install -r requirements.txt
```

**问题**: 数据库连接失败
```bash
# 解决方案: 检查数据库配置
# 编辑 config.py 中的 DB_CONFIG
```

**问题**: Telegram推送失败
```bash
# 解决方案: 检查Telegram配置
# 编辑 config.py 中的 TELEGRAM_CONFIG
```

### 2. 性能优化

**减少执行频率**:
```python
SCHEDULE_INTERVAL = 60  # 改为60秒
```

**减少获取数量**:
```python
# 在 config.py 中修改
'params': {
    'pagesize': '10',  # 减少到10条
}
```

**禁用推送**:
```python
# 临时禁用Telegram推送
# 注释掉推送相关代码
```

## 🔄 后台运行

### 1. Linux/Mac 后台运行
```bash
# 使用 nohup
nohup python ths.py > output.log 2>&1 &

# 使用 screen
screen -S news_crawler
python ths.py
# 按 Ctrl+A, D 分离会话

# 重新连接
screen -r news_crawler
```

### 2. Windows 后台运行
```bash
# 使用 start 命令
start /B python ths.py

# 或创建批处理文件 run.bat
@echo off
python ths.py
pause
```

### 3. 系统服务（高级）
可以将程序配置为系统服务，实现开机自启动：
- Linux: systemd service
- Windows: Windows Service
- Mac: launchd

## 📈 性能指标

### 1. 典型性能
- **执行时间**: 8-15秒/次
- **内存使用**: ~50MB
- **网络流量**: ~100KB/次
- **数据库操作**: 1-2次/执行

### 2. 资源消耗
- **CPU**: 低（仅在执行时占用）
- **内存**: 稳定（无内存泄漏）
- **磁盘**: 日志文件增长
- **网络**: 间歇性请求

## 💡 最佳实践

1. **监控日志**: 定期检查日志文件
2. **备份数据**: 定期备份数据库
3. **更新配置**: 根据需要调整参数
4. **错误处理**: 关注错误日志并及时处理
5. **资源管理**: 监控系统资源使用情况

## 🎯 总结

定时执行功能让财经新闻爬虫系统能够：
- ✅ 7×24小时不间断运行
- ✅ 自动获取最新财经新闻
- ✅ 智能去重避免重复推送
- ✅ 优雅处理错误和异常
- ✅ 提供详细的运行日志

现在您可以放心地让系统自动运行，及时获取最新的财经资讯！
